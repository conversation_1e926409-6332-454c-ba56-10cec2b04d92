# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/
.angular/


# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Docker and deployment files
.dockerignore
.env
.env.prod
Dockerfile
docker-compose.yml
docker-compose.prod.yml
docker/

# Build and deployment artifacts
build/
*.tar.gz
*.bin

# Database files
db.dump
db.dump.bkp
db1.dump
db1_converted.sql
db1_converted_data.sql
db1_converted_schema.sql

# SQL migration files
*.sql
src/main/resources/db/migration/

# Backup files
*.backup
data/employee-requests.json.backup

# Upload directories
uploads/

# Environment and configuration files
ops-excellence-8e03eb84e03b.json
src/assets/env.docker.js
src/assets/env.js

# Shell scripts
augment-evasion-toolkit.sh
clean-dump.sh
connect-vm.sh
convert_dump_to_sql.sh
create_different_dump_formats.sh
create_leads_request_details_table.sql
create-installer-package.sh
deploy-to-vm.sh
deploy.sh
device-identity-reset.sh
gcs_upload_and_fetch.sh
hardware-spoof.sh
ImportData.sh
install_dependecies.sh
oauth_migration.sql
quick-connect.sh
quick-start-docker.sh
quick_fix.sql
restore_dump_properly.sh
setup-meltano.sh
simple-deploy-test.sh
teamsphere-test-automation/
vm-deployment-script.sh
vm-setup.sh
vm_cleanup.sh
vpn-proxy-setup.sh

# Documentation
DATABASE_DUMP_INFO.md
README.md
SOLUTION_README.md
VM-CONNECTION-README.md
QUICK_START.md

# Role templates
ACCOUNT_MANAGER_Role_Template.csv
ADMIN_OPS_MANAGER_Role_Template.csv
LEAD_Role_Template.csv
MANAGER_Role_Template.csv
USER_Role_Template.csv

# Test files
.test.js.swp
TestVideo-2025-07-03_21.04.51

# Tokens directory
tokens/

# Logs directories
logs/
TeamSphereDebugLogs/
TeamSphereErrorLogs/


docker-browser.sh
org-chart/src/assets/env.docker.js
