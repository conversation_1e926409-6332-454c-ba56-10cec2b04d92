{"name": "org-chart", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.14", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "^16.2.14", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "file-saver": "^2.0.5", "gojs": "^3.0.15", "highcharts": "^12.1.2", "highcharts-angular": "^4.0.1", "jwt-decode": "^4.0.0", "papaparse": "^5.5.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.16", "@angular/cli": "^16.2.16", "@angular/compiler-cli": "^16.2.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "~4.3.0", "@types/papaparse": "^5.3.16", "@types/xlsx": "^0.0.36", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}