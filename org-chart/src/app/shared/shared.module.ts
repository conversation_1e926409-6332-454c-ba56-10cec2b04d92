import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

import { PageHeaderComponent } from '../page-header/page-header.component';
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';
import { ReusableTableComponent } from './reusable-table/reusable-table.component';
import { FormsModule } from '@angular/forms';
import { SharedToggleComponent } from './shared-toggle/shared-toggle.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';


@NgModule({
  declarations: [
    PageHeaderComponent,
    BreadcrumbComponent,
    ReusableTableComponent,
    SharedToggleComponent 
  ],
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatMenuModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatSlideToggleModule
  ],
  exports: [
    PageHeaderComponent,
    BreadcrumbComponent,
    ReusableTableComponent,
    SharedToggleComponent,
    MatSlideToggleModule,
    FormsModule
  ]
})
export class SharedModule { }
