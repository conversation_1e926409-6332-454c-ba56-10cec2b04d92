/* Container for the form */
.form-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Title Style */
.form-container h2 {
  margin-bottom: 24px;
  color: #333;
}

/* Use flexbox for the form fields to display six columns */
.form-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

/* Style for each individual mat-form-field in the row */
mat-form-field {
  width: 100%;
}

/* Mat-form-field label styling */
mat-label {
  font-size: 14px;
  color: #555;
}

/* Styling for the buttons container */
.buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

/* Individual button styling */
.action-button {
  width: 100%;
  height: 45px;
  font-size: 16px;
}

/* Primary button (Save) */
.action-button.save-button {
  background-color: #fff !important;
  color: #005cbf !important;
  border: 2px solid #005cbf !important;
}

.action-button.save-button:hover {
  background-color: #005cbf !important;
  color: #fff !important;
}

/* Warn button (Cancel) */
.action-button.cancel-button {
  background-color: #fff !important;
  color: #d32f2f !important;
  border: 2px solid #d32f2f !important;
}

.action-button.cancel-button:hover {
  background-color: #d32f2f !important;
  color: #fff !important;
}

/* Ensure consistent height for all form fields */
::ng-deep .mat-form-field-flex {
  align-items: center !important;
}

/* Make outline appearance consistent */
::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: rgba(0, 0, 0, 0.12);
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #3f51b5;
}

/* Ensure proper spacing for error messages */
::ng-deep .mat-form-field-subscript-wrapper {
  margin-top: 0.5em !important;
}

/* Ensure consistent padding */
::ng-deep .mat-form-field-wrapper {
  padding-bottom: 1.25em;
}

/* Style for required fields */
::ng-deep .mat-form-field-required-marker {
  color: #f44336;
}

/* Ensure proper alignment of datepicker toggle */
::ng-deep .mat-datepicker-toggle {
  color: rgba(0, 0, 0, 0.54);
}

/* Responsive design */
@media (max-width: 1200px) {
  .form-row {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .form-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .buttons {
    grid-template-columns: 1fr;
  }
  
  .action-button {
    width: 100%;
  }
}

.mat-tooltip {
  background-color: #2196f3;
  color: white;
  font-size: 14px;
}

.search-container {
  padding: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.search-input {
  width: 100%;
  border: none;
  outline: none;
  padding: 8px;
  font-size: 14px;
}

.search-input::placeholder {
  color: rgba(0, 0, 0, 0.38);
}

mat-select {
  width: 100%;
}

mat-option {
  height: auto;
  line-height: 1.5;
  padding: 8px;
}



/* Configuration button styles */
.config-button {
  color: #666 !important;
}

.config-button:hover {
  color: #3f51b5 !important;
}

