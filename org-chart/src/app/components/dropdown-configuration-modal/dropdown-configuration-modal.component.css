.dropdown-config-modal {
 width: 100%;
 max-width: 900px;
 min-height: 600px;
 max-height: 80vh;
 display: flex;
 flex-direction: column;
}


.modal-header {
 display: flex;
 justify-content: space-between;
 align-items: center;
 padding: 16px 24px;
 border-bottom: 1px solid #e0e0e0;
 margin: -24px -24px 0 -24px;
 background-color: #f8f9fa;
 border-top-left-radius: 4px;
 border-top-right-radius: 4px;
}


.modal-header h2 {
 margin: 0;
 color: #333;
 font-weight: 500;
 font-size: 20px;
}


.close-button {
 color: #666;
}


.close-button:hover {
 background-color: #e0e0e0;
}


.modal-content {
 padding: 24px;
 flex: 1;
 overflow-x: hidden;
 overflow-y: auto;
 display: flex;
 flex-direction: column;
 min-height: 0;
}


.form-section {
 margin-bottom: 24px;
 padding-bottom: 24px;
 border-bottom: 1px solid #e0e0e0;
}


.form-section h3 {
 margin: 0 0 16px 0;
 color: #333;
 font-weight: 500;
 font-size: 18px;
}


.dropdown-form {
 display: flex;
 flex-direction: column;
 gap: 16px;
}


.form-row {
 display: flex;
 gap: 16px;
 align-items: flex-start;
}


.form-field {
 flex: 1;
}


.checkbox-field {
 display: flex;
 align-items: center;
 margin-top: 8px;
 min-width: 120px;
}


.form-actions {
 display: flex;
 gap: 12px;
 margin-top: 16px;
}


.list-section {
 flex: 1;
 display: flex;
 flex-direction: column;
}


.list-section h3 {
 margin: 0 0 16px 0;
 color: #333;
 font-weight: 500;
 font-size: 18px;
}


.table-container {
 border: 1px solid #e0e0e0;
 border-radius: 4px;
 overflow-x: auto;
 overflow-y: auto;
 flex: 1;
 display: flex;
 flex-direction: column;
 max-height: 400px;
 min-height: 300px;
}


.options-table {
 width: 100%;
 border-collapse: collapse;
 min-width: 100%;
}


.options-table th {
 background-color: #f5f5f5;
 font-weight: 600;
 color: #333;
 padding: 12px 16px;
 text-align: left;
 border-bottom: 1px solid #e0e0e0;
 position: sticky;
 top: 0;
 z-index: 10;
}


.options-table td {
 padding: 12px 16px;
 border-bottom: 1px solid #e0e0e0;
}


.options-table tr:hover {
 background-color: #f9f9f9;
}


.status-active {
 color: #4caf50;
 font-weight: 500;
 padding: 4px 8px;
 border-radius: 4px;
 background-color: #e8f5e9;
}


.status-inactive {
 color: #f44336;
 font-weight: 500;
 padding: 4px 8px;
 border-radius: 4px;
 background-color: #ffebee;
}


.delete-button {
 color: #f44336;
}


.delete-button:hover {
 background-color: rgba(244, 67, 54, 0.1);
}


.no-data {
 padding: 32px;
 text-align: center;
 color: #666;
 font-style: italic;
 flex: 1;
 display: flex;
 align-items: center;
 justify-content: center;
}


.loading-container {
 display: flex;
 flex-direction: column;
 align-items: center;
 padding: 32px;
 gap: 16px;
 flex: 1;
 justify-content: center;
}


.loading-container p {
 margin: 0;
 color: #666;
}


.modal-actions {
 padding: 16px 24px;
 border-top: 1px solid #e0e0e0;
 margin: 0 -24px -24px -24px;
 justify-content: flex-end;
 background-color: #f8f9fa;
 border-bottom-left-radius: 4px;
 border-bottom-right-radius: 4px;
}


/* Responsive design */
@media (max-width: 600px) {
 .dropdown-config-modal {
   max-width: 100%;
   min-height: auto;
 }
  .form-row {
   flex-direction: column;
   gap: 8px;
 }
  .checkbox-field {
   margin-top: 0;
 }
  .modal-content {
   padding: 16px;
   overflow-x: auto;
 }
  .options-table td,
 .options-table th {
   padding: 8px 12px;
   font-size: 14px;
 }
  .modal-header,
 .modal-actions {
   margin-left: -16px;
   margin-right: -16px;
 }
  .modal-header {
   margin-top: -16px;
   padding: 12px 16px;
 }
  .modal-actions {
   margin-bottom: -16px;
   padding: 12px 16px;
 }
}


/* Material Design overrides */
::ng-deep .mat-dialog-container {
 padding: 0 !important;
 border-radius: 4px !important;
 overflow: hidden;
}


::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
 color: #e0e0e0;
}


::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
 color: #1976d2;
}


::ng-deep .mat-checkbox-checked .mat-checkbox-background {
 background-color: #1976d2;
}


::ng-deep .mat-raised-button.mat-primary {
 background-color: #1976d2;
 color: white;
}


/* Fix for scrollbar issues */
::ng-deep .mat-dialog-content {
 overflow: hidden !important;
}





