<div class="project-form-container">
  <h2 mat-dialog-title>{{ isEditMode ? 'Edit Project' : 'Add Project' }}</h2>
  
  <form [formGroup]="projectForm" (ngSubmit)="onSubmit()">
    <div mat-dialog-content>
      <!-- Project Name -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Project Name</mat-label>
        <input matInput formControlName="name" required>
        <mat-error *ngIf="projectForm.get('name')?.hasError('required')">
          Project name is required
        </mat-error>
        <mat-error *ngIf="projectForm.get('name')?.hasError('maxlength')">
          Project name cannot exceed 100 characters
        </mat-error>
      </mat-form-field>

      <!-- Project Code -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Project Code</mat-label>
        <input matInput formControlName="code" required [readonly]="isEditMode">
        <mat-hint>Use only uppercase letters, numbers, hyphens, and underscores</mat-hint>
        <mat-error *ngIf="projectForm.get('code')?.hasError('required')">
          Project code is required
        </mat-error>
        <mat-error *ngIf="projectForm.get('code')?.hasError('pattern')">
          Project code can only contain uppercase letters, numbers, hyphens, and underscores
        </mat-error>
        <mat-error *ngIf="projectForm.get('code')?.hasError('maxlength')">
          Project code cannot exceed 20 characters
        </mat-error>
      </mat-form-field>

      <!-- Project Description -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Description</mat-label>
        <textarea matInput formControlName="description" rows="4" required></textarea>
        <mat-hint align="end">{{ projectForm.get('description')?.value?.length || 0 }}/500</mat-hint>
        <mat-error *ngIf="projectForm.get('description')?.hasError('required')">
          Description is required
        </mat-error>
        <mat-error *ngIf="projectForm.get('description')?.hasError('maxlength')">
          Description cannot exceed 500 characters
        </mat-error>
      </mat-form-field>

      <!-- Project Start Date -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Start Date</mat-label>
        <input matInput [matDatepicker]="startDatePicker" formControlName="startDate" required>
        <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #startDatePicker></mat-datepicker>
        <mat-error *ngIf="projectForm.get('startDate')?.hasError('required')">
          Start date is required
        </mat-error>
      </mat-form-field>

      <!-- Project End Date -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>End Date</mat-label>
        <input matInput [matDatepicker]="endDatePicker" formControlName="endDate">
        <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #endDatePicker></mat-datepicker>
        <mat-hint>Optional</mat-hint>
      </mat-form-field>

      <!-- Project Status -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Status</mat-label>
        <mat-select formControlName="status" required>
          <mat-option *ngFor="let status of statusOptions" [value]="status">
            {{ status }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="projectForm.get('status')?.hasError('required')">
          Status is required
        </mat-error>
      </mat-form-field>

      <!-- Overtime Eligibility Toggle -->
      <div class="full-width" style="margin: 16px 0;">
        <mat-slide-toggle formControlName="isOvertimeEligible" color="primary">
          Enable Overtime for this Project
        </mat-slide-toggle>
        <mat-hint style="display: block; margin-top: 8px; font-size: 12px; color: #666;">
          When enabled, team members can log overtime hours for this project
        </mat-hint>
      </div>
    </div>

    <div mat-dialog-actions align="end">
      <button mat-button type="button" (click)="onCancel()">Cancel</button>
      <button mat-raised-button color="primary" type="submit" [disabled]="projectForm.invalid">
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </div>
  </form>
</div>
