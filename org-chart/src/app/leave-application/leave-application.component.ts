import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { NotificationService } from 'src/app/shared/notification.service';
import { distinctUntilChanged } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { LeaveService } from '../services/leave.service';
import { formatDate } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { ChangeDetectorRef } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AttendanceComponent } from './attendance/attendance.component';
import { NewRequestComponent } from '../leave-application/components/new-request/new-request.component';
import { HistoryComponent } from '../leave-application/components/history/history.component';
import { EmployeeService } from '../employee.service';
import { HttpClient } from '@angular/common/http';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { environment } from 'src/environments/environment';
import { ConfirmationDialogComponent } from 'src/app/confirm-dialog/confirmation-dialog.component';

interface BaseResponse<T> {
  status: string;
  code: number;
  message: string;
  data: T;
}


interface SelectOption {
  value: string;
  label: string;
}

export interface VunnoMgmtDto {
  ldap: string;
  name: string;
  role: string;
  email: string;
  programAlignment: string;
  team: string;
  lead: string;
}

interface LeaveRecord {
  status: string;  // Changed from union type to string
  applicationType: string,
  type: string;
  startDate: Date;
  endDate: Date;
  duration: string;
  approver: string;
}

interface Holiday {
  id: number;
  holidayDate: string;
  holidayName: string;
  description?: string;
  holidayType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt?: string;
  uploadedBy?: string;
}

@Component({
  selector: 'app-leave-application',
  templateUrl: './leave-application.component.html',
  styleUrls: ['./leave-application.component.css']
})

export class LeaveApplicationComponent implements OnInit {

  activeTab: string = 'attendance'; // Default active tab
  ldapProvided: boolean = false;
  loading = false;
  loadingMessage = "";
  submissionMessage: string = '';
  showSuccess: boolean = false;
  leaveForm!: FormGroup;
  submitted = false;
  requestorName = 'vrajoriya'; // For Testing Purposes
  requestorEmail = '<EMAIL>'; // For Testing Purposes
  approverEmail = '<EMAIL>'; // For Testing Purposes
  leaveBalance = { sick: 0, casual: 0, earned: 0, total: 0, totalwfh: 0.0, qtrwfh: 0.0, };
  leaveHistory: LeaveRecord[] = [];
  userRole: string | undefined;
  status = "PENDING";
  selectedFile: File | null = null;

  // Holiday management properties
  baseUrl = environment.apiUrl;
  holidayDataSource = new MatTableDataSource<Holiday>();
  holidayDisplayedColumns: string[] = ['holidayDate', 'holidayName', 'description', 'holidayType', 'uploadedBy', 'actions'];
  selectedHolidayFile: File | null = null;
  isUploadingHolidays = false;
  canUploadHolidays = false;

  showApproverDropdown = false;
  approverOptions: string[] = [];

  // WFH disclaimer properties
  showWFHDisclaimer = false;
  wfhDisclaimerMessage = '';

  // End date control property
  isEndDateDisabled = false;


  @ViewChild('attendanceComp') attendanceComponent?: AttendanceComponent;
  @ViewChild(NewRequestComponent) newRequestComponent?: NewRequestComponent;
  @ViewChild(HistoryComponent) historyComponent?: HistoryComponent;
  @ViewChild(MatPaginator) holidayPaginator!: MatPaginator;
  @ViewChild(MatSort) holidaySort!: MatSort;
  @ViewChild('holidayFileInput') holidayFileInput!: ElementRef;

  constructor(private fb: FormBuilder, private leaveService: LeaveService, private dialog: MatDialog, private cdRef: ChangeDetectorRef, private snackbar: MatSnackBar,
    private notificationService: NotificationService, private employeeService: EmployeeService, private http: HttpClient
  ) { }

  private markFormGroupTouched(formGroup: FormGroup | FormControl): void {
    if (formGroup instanceof FormControl) {
      formGroup.markAsTouched();
      return;
    }

    Object.values(formGroup.controls).forEach(control => {
      if (control instanceof FormGroup || control instanceof FormControl) {
        this.markFormGroupTouched(control);
      }
    });
}

applyHolidayFilter(event: Event): void {
  const filterValue = (event.target as HTMLInputElement).value;
  this.holidayDataSource.filter = filterValue.trim().toLowerCase();

  if (this.holidayDataSource.paginator) {
    this.holidayDataSource.paginator.firstPage();
  }
}

onHolidayFileSelected(event: any): void {
  const file = event.target.files[0];
  if (file && file.type === 'text/csv') {
    this.selectedHolidayFile = file;
  } else {
    this.notificationService.showNotification({
      type: 'error',
      message: 'Please select a valid CSV file'
    });
    this.selectedHolidayFile = null;
  }
}

uploadHolidayCSV(): void {
  if (!this.selectedHolidayFile) {
    this.notificationService.showNotification({
      type: 'error',
      message: 'Please select a CSV file first'
    });
    return;
  }

  if (!this.isAdminOpsManager()) {
    this.notificationService.showNotification({
      type: 'error',
      message: 'You do not have permission to upload holidays'
    });
    return;
  }

  this.isUploadingHolidays = true;
  const formData = new FormData();
  formData.append('file', this.selectedHolidayFile);

  this.http.post<BaseResponse<Holiday[]>>(`${this.baseUrl}/api/holidays/upload-csv`, formData)
    .subscribe({
      next: (response) => {
        this.isUploadingHolidays = false;
        if (response.status === 'success') {
          this.notificationService.showNotification({
            type: 'success',
            message: response.message || 'Holidays uploaded successfully!'
          });
          this.loadHolidays();
          this.selectedHolidayFile = null;
          // Reset file input
          if (this.holidayFileInput) {
            this.holidayFileInput.nativeElement.value = '';
          }
        } else {
          this.notificationService.showNotification({
            type: 'error',
            message: response.message || 'Failed to upload holidays'
          });
        }
      },
      error: (error) => {
        this.isUploadingHolidays = false;
        console.error('Error uploading CSV:', error);
        let errorMessage = 'Failed to upload holidays. Please try again.';
        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        }
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
}

deleteHoliday(holiday: Holiday): void {
  if (!this.isAdminOpsManager()) {
    this.notificationService.showNotification({
      type: 'error',
      message: 'You do not have permission to delete holidays'
    });
    return;
  }

  const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
    data: {
      title: 'Delete Holiday',
      message: `Are you sure you want to delete the holiday "${holiday.holidayName}"?`,
      confirmButtonText: 'Delete',
      color: 'warn'
    }
  });

  dialogRef.afterClosed().subscribe(result => {
    if (result === true) {
      this.http.delete<BaseResponse<string>>(`${this.baseUrl}/api/holidays/${holiday.id}`)
        .subscribe({
          next: (response) => {
            if (response.status === 'success') {
              this.notificationService.showNotification({
                type: 'success',
                message: 'Holiday deleted successfully!'
              });
              this.loadHolidays();
            } else {
              this.notificationService.showNotification({
                type: 'error',
                message: response.message || 'Failed to delete holiday'
              });
            }
          },
          error: (error) => {
            console.error('Error deleting holiday:', error);
            this.notificationService.showNotification({
              type: 'error',
              message: 'Failed to delete holiday. Please try again.'
            });
          }
        });
    }
  });
}

downloadSampleHolidayCSV(): void {
  const csvContent = `Date,Holiday,Description
1/1/2025,New Year's Day,New Year celebration
8/15/2025,Independence Day,Indian Independence Day
12/25/2025,Christmas Day,Christmas celebration`;

  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'holidays_sample.csv';
  a.click();
  window.URL.revokeObjectURL(url);
}

  isRoleAllowed(): boolean {
    const allowedRoles = ['LEAD', 'MANAGER', 'ADMIN_OPS_MANAGER'];
    return this.userRole !== undefined && allowedRoles.includes(this.userRole);
  }

  // Update your setActiveTab method
  setActiveTab(tab: string): void {
    this.activeTab = tab;

    // Load data for specific tabs
    if (tab === 'history') {
      // this.fetchLeaveHistory();
    }
    else if (tab === 'balance') {}
    else if (tab === 'attendance') {
      setTimeout(() => {
        this.attendanceComponent?.refreshAttendance();
      }, 0);
    }
    else if (tab === 'holidays') {
      setTimeout(() => {
        this.loadHolidays();
      }, 0);
    }
  }

  // Holiday Management Methods
  isAdminOpsManager(): boolean {
    return this.userRole === 'ADMIN_OPS_MANAGER';
  }

  loadHolidays(): void {
    this.http.get<BaseResponse<Holiday[]>>(`${this.baseUrl}/api/holidays`)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.holidayDataSource.data = response.data;
          } else {
            this.notificationService.showNotification({
              type: 'error',
              message: response.message || 'Failed to load holidays'
            });
          }
        },
        error: (error) => {
          console.error('Error loading holidays:', error);
          this.notificationService.showNotification({
            type: 'error',
            message: 'Failed to load holidays. Please try again.'
          });
        }
      });
  }

  // Options for select fields
  requestTypeOptions: SelectOption[] = [
    { value: 'Leave', label: 'Leave' },
    { value: 'Work From Home', label: 'Work From Home' }
  ];

  leaveTypeOptions: SelectOption[] = [];

  durationTypeOptions: SelectOption[] = [
    { value: 'Full Day', label: 'Full Day' },
    { value: 'Half Day AM', label: 'Half Day (AM)' },
    { value: 'Half Day PM', label: 'Half Day (PM)' },
    { value: 'Multiple Days', label: 'Multiple Days' },
  ];

  ngOnInit(): void {
    this.userRole = localStorage.getItem('role') || undefined;
    this.canUploadHolidays = this.isAdminOpsManager();
    this.initForm();

    this.leaveService.getCurrentUserLdap().subscribe({
      next: (response) => {
        const ldap = response.data.ldap;
        this.leaveForm.get('ldap')?.setValue(ldap);
        this.fetchManager();
      },
      error: (err) => console.error('Error fetching current user:', err)
    });

    this.setupFormSubscriptions();

    // Load holidays
    this.loadHolidays();

    // Set up holiday table filter
    this.holidayDataSource.filterPredicate = (data: Holiday, filter: string) => {
      return Object.values(data)
        .some(value => value?.toString().toLowerCase().includes(filter.toLowerCase()));
    };
  }

  ngAfterViewInit(): void {
    if (this.holidayPaginator && this.holidaySort) {
      this.holidayDataSource.paginator = this.holidayPaginator;
      this.holidayDataSource.sort = this.holidaySort;
    }
  }

  formatDate(date: Date): string {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  }

  get f() {
    return this.leaveForm.controls;
  }


  private setupFormSubscriptions(): void {
    // LDAP changes
    this.leaveForm.get('ldap')?.valueChanges.subscribe(ldap => {
      this.ldapProvided = !!ldap && ldap.trim() !== '';
      this.updateApproverFieldState(ldap);
    });

    // Duration type changes - use distinctUntilChanged to prevent multiple calls
    this.leaveForm.get('durationType')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(value => {
      console.log("Duration type changed to:", value);
      this.updateTimes(value);
      this.updateEndDateRequirement(value);
    });

    // Request type changes
    this.leaveForm.get('requestType')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(requestType => {
      this.updateLeaveTypes(requestType);
      this.updateUIBasedOnRequestType(requestType);
      // Dynamically set validator for reason
      const reasonControl = this.leaveForm.get('reason');
      if (requestType === 'Work From Home') {
        reasonControl?.setValidators([Validators.required]);
      } else {
        reasonControl?.clearValidators();
      }
      reasonControl?.updateValueAndValidity();

      // Check for WFH disclaimer
      this.checkWFHDisclaimer();
    });

    // Start date changes
    this.leaveForm.get('startDate')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(startDate => {
      const durationType = this.leaveForm.get('durationType')?.value;
      if (startDate && durationType) {
        // Update end date requirement based on current duration type
        this.updateEndDateRequirement(durationType);
      }
      // Check for WFH disclaimer when dates change
      this.checkWFHDisclaimer();
    });

    // End date changes
    this.leaveForm.get('endDate')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(() => {
      // Check for WFH disclaimer when dates change
      this.checkWFHDisclaimer();
    });

    // Leave type changes
    this.leaveForm.get('leaveType')?.valueChanges.pipe(
      distinctUntilChanged()
    ).subscribe(value => {
      if (value === 'Long Leave') {
        this.leaveForm.get('durationType')?.setValue('Multiple Days');
        this.leaveForm.get('durationType')?.disable();
        // For Long Leave, enable end date field and require it
        this.isEndDateDisabled = false;
        this.leaveForm.get('endDate')?.enable();
        this.leaveForm.get('endDate')?.setValidators([Validators.required]);
      } else {
        this.leaveForm.get('durationType')?.enable();
        this.leaveForm.get('endDate')?.clearValidators();
        // Reset end date disabled state when not Long Leave
        const durationType = this.leaveForm.get('durationType')?.value;
        if (durationType) {
          this.updateEndDateRequirement(durationType);
        }
      }
      this.leaveForm.get('endDate')?.updateValueAndValidity();
    });
  }

  private initForm(): void {
    this.leaveForm = this.fb.group({
      ldap: ['', Validators.required], // Make disabled
      approver: ['', Validators.required],
      requestType: ['', Validators.required],
      leaveType: ['', Validators.required],
      durationType: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: [''],
      backupInfo: ['', Validators.required],
      reason: ['',],
      document: [null], // optional, handled in method
      oooProof: ['', Validators.required],
      timesheetProof: ['', Validators.required],
      startTime: ['07:30', Validators.required],
      endTime: ['16:30', Validators.required]
    }, { validators: this.dateRangeValidator });

    // Special handling for Long Leave
    this.leaveForm.get('leaveType')?.valueChanges.subscribe(value => {
      if (value === 'Long Leave') {
        this.leaveForm.get('durationType')?.setValue('Multiple Days');
        this.leaveForm.get('durationType')?.disable();
        // For Long Leave, enable end date field and require it
        this.isEndDateDisabled = false;
        this.leaveForm.get('endDate')?.enable();
        this.leaveForm.get('endDate')?.setValidators([Validators.required]);
      } else {
        this.leaveForm.get('durationType')?.enable();
        this.leaveForm.get('endDate')?.clearValidators();
        this.leaveForm.get('endDate')?.setValidators([Validators.required]);
      }
      this.leaveForm.get('endDate')?.updateValueAndValidity();
    });

    this.setupFormSubscriptions();
  }

  dateRangeValidator(formGroup: FormGroup) {
    const startDate = formGroup.get('startDate')?.value;
    const endDate = formGroup.get('endDate')?.value;
    const durationType = formGroup.get('durationType')?.value;

    if (!startDate || !endDate) {
      return null;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Start date cannot be greater than end date
    if (start > end) {
      return { dateRange: 'Start date cannot be after end date' };
    }

    // For Multiple Days duration, dates cannot be equal
    if (durationType === 'Multiple Days' && start.getTime() === end.getTime()) {
      return { dateRange: 'For Multiple Days duration, start and end dates cannot be the same' };
    }

    return null;
  }

  updateLeaveTypes(requestType: string): void {
    if (requestType === 'Work From Home') {
      this.leaveForm.get('leaveType')?.disable();
    }
    else {
      this.leaveTypeOptions = [
        { value: 'Sick Leave', label: 'Sick Leave' },
        { value: 'Earned Leave', label: 'Earned Leave' },
        { value: 'Casual Leave', label: 'Casual Leave' },
      ];
      this.leaveForm.get('leaveType')?.enable();
    }
    this.leaveForm.get('leaveType')?.setValue('');
  }

  updateApproverFieldState(ldap: string): void {
    const approverControl = this.leaveForm.get('approver');

    if (!ldap || ldap.trim() === '') {
      approverControl?.disable();
      approverControl?.reset(); // Clear the value when disabled
    } else {
      approverControl?.enable();
    }
  }

  fetchManager(): void {
    const ldap = this.leaveForm.get('ldap')?.value;
    if (ldap) {
      this.leaveService.getManagerByLdap(ldap).subscribe({
        next: (managerDetails) => {
          if (managerDetails) {
            const user = managerDetails[0]; 

            console.log("Manager details ", user);
  
            this.leaveForm.get('approver')?.setValue(user.lead);
            this.approverOptions = [user.lead];
  
            this.leaveService.setManagerDetails(user);

          }
        },
        error: (err) => {
          console.error('Error fetching manager:', err);
          this.loading = false;
        }
      });
    }
  }

  updateUIBasedOnRequestType(requestType: string): void {
    const isWFH = requestType === 'Work From Home';

    // Toggle Leave Type
    const leaveTypeControl = this.leaveForm.get('leaveType');
    if (isWFH) {
      leaveTypeControl?.disable();
      leaveTypeControl?.setValue('');
    } else {
      leaveTypeControl?.enable();
    }

    // Toggle the three fields
    const fieldsToToggle = ['oooProof', 'timesheetProof', 'backupInfo'];
    fieldsToToggle.forEach(field => {
      const control = this.leaveForm.get(field);
      if (isWFH) {
        control?.disable();
        control?.setValue('');
        control?.clearValidators();
      } else {
        control?.enable();
        control?.setValidators([Validators.required]);
      }
      control?.updateValueAndValidity();
    });

    // Always require duration type
    this.leaveForm.get('durationType')?.setValidators([Validators.required]);
    this.leaveForm.get('durationType')?.updateValueAndValidity();
  }

  get isWFH(): boolean {
    return this.leaveForm.get('requestType')?.value === 'Work From Home';
  }

  onRequestTypeChange(): void {
    const requestType = this.leaveForm.get('requestType')?.value;

    if (requestType === 'Work From Home') {
      this.leaveForm.get('leaveType')?.setValue('');
      this.leaveForm.get('leaveType')?.disable(); // Disable Leave Type
    } else {
      this.leaveForm.get('leaveType')?.enable(); // Enable Leave Type if not WFH
    }
    this.updateUIBasedOnRequestType(requestType);
  }

  updateTimes(durationType: string): void {
    // Set default times based on duration type
    if (durationType === 'Half Day AM') {
      this.leaveForm.get('startTime')?.setValue('07:30');
      this.leaveForm.get('endTime')?.setValue('12:00');
    } else if (durationType === 'Half Day PM') {
      this.leaveForm.get('startTime')?.setValue('12:00');
      this.leaveForm.get('endTime')?.setValue('16:30');
    } else if (durationType === 'Full Day') {
      this.leaveForm.get('startTime')?.setValue('07:30');
      this.leaveForm.get('endTime')?.setValue('16:30');
    }
  }

  updateEndDateRequirement(durationType: string): void {
    const endDateControl = this.leaveForm.get('endDate');
    const startDate = this.leaveForm.get('startDate')?.value;

    if (!startDate) return;

    if (durationType === 'Multiple Days' ||
      (this.leaveForm.get('leaveType')?.value === 'Long Leave' &&
        this.leaveForm.get('requestType')?.value === 'Leave')) {
      // For multiple days, enable end date field and require it
      this.isEndDateDisabled = false;
      endDateControl?.enable();
      endDateControl?.setValidators([Validators.required]);
    }
    else if (['Full Day', 'Half Day AM', 'Half Day PM'].includes(durationType)) {
      // For single day durations, set end date to same as start date and disable the field
      endDateControl?.setValue(startDate);
      endDateControl?.clearValidators();
      this.isEndDateDisabled = true;
      endDateControl?.disable();
    }
    else {
      // Default case
      this.isEndDateDisabled = false;
      endDateControl?.enable();
      endDateControl?.clearValidators();
    }

    endDateControl?.updateValueAndValidity();
  }

  onDurationTypeChange(): void {
    console.log("onDurationTypeChange function started");
    const durationType = this.leaveForm.get('durationType')?.value;
    this.updateTimes(durationType);
    this.updateEndDateRequirement(durationType);

    // Check for WFH disclaimer
    this.checkWFHDisclaimer();

    console.log("onDurationTypeChange function ended");
  }

  /**
   * Calculate the number of days for a WFH request
   */
  calculateWFHDays(): number {
    const durationType = this.leaveForm.get('durationType')?.value;
    const startDate = this.leaveForm.get('startDate')?.value;
    const endDate = this.leaveForm.get('endDate')?.value;

    if (!durationType) return 0;

    switch (durationType) {
      case 'Full Day':
        return 1;
      case 'Half Day AM':
      case 'Half Day PM':
        return 0.5;
      case 'Multiple Days':
        if (startDate && endDate) {
          const start = new Date(startDate);
          const end = new Date(endDate);
          const timeDiff = end.getTime() - start.getTime();
          const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // Inclusive
          return daysDiff;
        }
        return 0;
      default:
        return 0;
    }
  }

  /**
   * Check if WFH disclaimer should be shown
   */
  checkWFHDisclaimer(): void {
    const requestType = this.leaveForm.get('requestType')?.value;

    if (requestType === 'Work From Home') {
      const wfhDays = this.calculateWFHDays();

      if (wfhDays >= 3) {
        this.showWFHDisclaimer = true;
        this.wfhDisclaimerMessage = `This WFH request is for ${wfhDays} days (>=3 days). It will be redirected to the Account Manager for approval.`;
      } else {
        this.showWFHDisclaimer = false;
        this.wfhDisclaimerMessage = '';
      }
    } else {
      this.showWFHDisclaimer = false;
      this.wfhDisclaimerMessage = '';
    }
  }


  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      this.selectedFile = input.files[0];
    }
  }


  onSubmit(): void {
    this.submitted = true;
    this.markFormGroupTouched(this.leaveForm);

    // Debug authentication
    const token = localStorage.getItem('authToken');
    const role = localStorage.getItem('role');
    const username = localStorage.getItem('username');

    console.log('Authentication Debug:', {
      hasToken: !!token,
      tokenLength: token?.length,
      role: role,
      username: username
    });

    if (!token) {
      this.notificationService.showNotification({
        type: 'error',
        message: 'You are not logged in. Please login first.'
      });
      return;
    }

    const requestType = this.leaveForm.get('requestType')?.value;
    const durationValue = this.leaveForm.get('durationType')?.value;
    console.log("App ", this.leaveForm.get('applicationType')?.value, " requestType ", requestType)

    this.updateUIBasedOnRequestType(requestType);
    this.updateEndDateRequirement(durationValue);
    this.leaveForm.updateValueAndValidity();

    Object.keys(this.leaveForm.controls).forEach(controlName => {
      const control = this.leaveForm.get(controlName);
      console.log(`${controlName}: valid=${control?.valid}, value=${control?.value}`);
    });


    if (this.leaveForm.invalid) {
      console.warn('Form is invalid. Details:', this.leaveForm.value, this.leaveForm.errors);
      return;
    }

    // If end date is undefined/empty, set it to start date
    const endDate = this.leaveForm.value.endDate;
    const startDate = this.leaveForm.value.startDate;
    const finalEndDate = (!endDate || endDate === '') ? startDate : endDate;
    console.log('Final end date:', finalEndDate);

    const payload = {
      ldap: this.leaveForm.value.ldap,
      approvingLead: this.leaveForm.value.approver,
      applicationType: this.leaveForm.value.requestType,
      leaveType: this.leaveForm.value.leaveType || '',
      lvWfhDuration: this.leaveForm.value.durationType,
      startDate: startDate ? formatDate(startDate, 'yyyy-MM-dd', 'en-US') : '',
      endDate: finalEndDate ? formatDate(finalEndDate, 'yyyy-MM-dd', 'en-US') : '',
      startDateTime: this.leaveForm.value.startTime,
      endDateTime: this.leaveForm.value.endTime,
      oooProof: this.getProofValue(this.leaveForm.value.oooProof),
      timesheetProof: this.getProofValue(this.leaveForm.value.timesheetProof),
      backupInfo: this.getProofValue(this.leaveForm.value.backupInfo),
      reason: this.leaveForm.value.reason || 'NA',
      status: this.status,
      role: this.userRole
    };

    const formData = new FormData();
    formData.append('leaveRequest', new Blob([JSON.stringify(payload)], { type: 'application/json' }));

    if (requestType === 'Work From Home' && this.selectedFile) {
      formData.append('document', this.selectedFile);
    }

    this.leaveService.submitLeaveRequest(formData).subscribe({
      next: (response) => {
        if (response?.message?.includes('Requested leave successfully saved into database')) {
          this.notificationService.showNotification({ type: 'success', message: 'Request updated successfully.' });
          this.resetForm();
        }
        else{
          this.notificationService.showNotification({ type: 'warning', message: 'Leave requested. Mail not sent — please contact your lead.' });
        }
      },
      error: (error) => {
        console.error('Submission error:', error);
        this.notificationService.showNotification({ type: 'error', message: 'Leave request not successful, Please try again!' });
      }
    });
  }

  triggerHolidayFileInput(): void {
    this.holidayFileInput.nativeElement.click();
  }

  private getProofValue(value: any): string {
    if (value === null || value === undefined || value === '') {
      return "NA,(WFH)";
    }
    return value;
  }

  resetForm(): void {
    // Clear only the fields that should be reset
    this.leaveForm.patchValue({
      requestType: '',
      leaveType: '',
      durationType: '',
      startDate: '',
      endDate: '',
      backupInfo: '',
      oooProof: '',
      timesheetProof: '',
      startTime: '07:30',
      endTime: '04:30'
    });

    // Clear specific fields that shouldn't keep values
    this.leaveForm.get('requestType')?.setValue('');
    this.leaveForm.get('leaveType')?.setValue('');
    this.leaveForm.get('durationType')?.setValue('');
    this.leaveForm.get('startDate')?.setValue('');
    this.leaveForm.get('endDate')?.setValue('');

    // Reset end date disabled state
    this.isEndDateDisabled = false;
    this.leaveForm.get('endDate')?.enable();

    // Reset WFH disclaimer
    this.showWFHDisclaimer = false;
    this.wfhDisclaimerMessage = '';

    // Reset form state
    this.leaveForm.markAsPristine();
    this.leaveForm.markAsUntouched();
    this.submitted = false;
    this.loading = false;
    this.leaveTypeOptions = []
    this.submitted = false;
    this.cdRef.detectChanges();
  }
}