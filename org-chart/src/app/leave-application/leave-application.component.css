/* Container for the form */
.form-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.tab-content {
  padding: 20px 0;
}

/* Title Style */
.form-container h2, .header h1 {
  margin-bottom: 24px;
  color: #333;
  font-size: 32px;
  font-weight: 500;
}

.header {
  text-align: center;
  margin-bottom: 20px;
}

.header p {
  color: #555;
  font-size: 14px;
  margin-bottom: 20px;
}

/* Use flexbox for the form fields to display in grid */
.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

/* Full width row for certain fields */
.form-row.full-width {
  grid-template-columns: 1fr;
}

.full-width-field {
  width: 100%;
}

/* Make textarea taller */
textarea {
  min-height: 80px;
}

/* Style for each individual form field in the row */
.form-group {
  width: 100%;
}

/* Form field label styling */
.form-group label {
  display: block;
  font-size: 14px;
  color: #555;
  margin-bottom: 8px;
}

/* Form control styling */
.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
}

.form-control:focus {
  outline: none;
  border-color: #3f51b5;
}

/* Styling for the buttons container */
.buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

/* Individual button styling */
.btn {
  width: 100%;
  height: 45px;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
}

/* Primary button (Submit) */
.btn-primary {
  background-color: #fff !important;
  color: #005cbf !important;
  border: 2px solid #005cbf !important;
}

.btn-primary:hover {
  background-color: #005cbf !important;
  color: #fff !important;
}

/* Secondary button (Cancel) */
.btn-secondary {
  background-color: #fff !important;
  color: #d32f2f !important;
  border: 2px solid #d32f2f !important;
}

.btn-secondary:hover {
  background-color: #d32f2f !important;
  color: #fff !important;
}

/* Tab styles */
.tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 25px;
}

.tab {
  padding: 10px 20px;
  cursor: pointer;
  color: #888;
  font-size: 14px;
  position: relative;
}

.tab.active {
  color: #3f51b5;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #3f51b5;
}

/* Form section styles */
.form-section {
  margin-bottom: 30px;
}

.form-section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  color: #3f51b5;
}

.form-section-icon {
  background-color: #e8f0fe;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 30px 0;
}

/* Date picker styling */
.date-input-container {
  position: relative;
}

.date-picker-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #3f51b5;
}

/* Icon styles */
.icon {
  width: 18px;
  height: 18px;
  fill: #3f51b5;
}

::ng-deep .mat-form-field-flex {
    background-color: white !important;
    padding: 0 !important;
    border: 1px solid #ddd !important;
    border-radius: 5px !important;
}

::ng-deep .mat-form-field-underline {
    display: none;
}

::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
}

::ng-deep .mat-form-field-infix {
    padding: 0.5em 0;
    border-top: 0;
}

/* leave-history.component.css */
.leave-history-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  h1 {
    color: #333;
    font-size: 24px;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .leave-history-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }

  .leave-history-table th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #555;
    border-bottom: 2px solid #ddd;
  }

  .leave-history-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    color: #333;
  }

  .leave-history-table tr:last-child td {
    border-bottom: none;
  }

  .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }

  .status-approved {
    background-color: #e6f7ee;
    color: #00a854;
  }

  .status-pending {
    background-color: #fff7e6;
    color: #fa8c16;
  }

  .status-rejected {
    background-color: #fff1f0;
    color: #f5222d;
  }

  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
  }

  .showing-text {
    font-size: 14px;
    color: #666;
  }

  .pagination-controls {
    display: flex;
    gap: 5px;
    align-items: center;
  }


  .pagination-controls button,
.pagination-controls span {
    padding: 5px 10px;
    cursor: pointer;
    border: 1px solid #ddd;
    background: white;
    border-radius: 3px;
    min-width: 30px;
    text-align: center;
}

  .pagination-controls button:hover {
    background-color: #f5f5f5;
  }

  .pagination-controls span.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }

  .pagination-controls button:hover:not(:disabled),
.pagination-controls span:hover:not(.active) {
    background-color: #f0f0f0;
}

  .pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}


  .pagination-info {
    margin-top: 8px;
    font-size: 0.9em;
    color: #666;
  }

.leave-history-container {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .leave-history-header {
    margin-bottom: 20px;
  }

  .search-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
  }

  .search-input {
    position: relative;
    flex-grow: 1;
  }

  .search-input input {
    width: 100%;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
  }

  .search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
  }

  .status-filter select,
  .page-size-filter select {
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
  }

  .styled-table {
    width: 100%;
    border-collapse: collapse;
  }

  .styled-table th {
    background-color: #f5f5f5;
    color: #333;
    font-weight: 600;
    padding: 12px;
    text-align: left;
    border-bottom: 2px solid #e0e0e0;
  }

  .styled-table td {
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
  }

  .styled-table .main-row:hover {
    background-color: #f9f9f9;
    cursor: pointer;
  }

  .approved {
    color: green;
    font-weight: bold;
  }

  .pending {
    color: orange;
    font-weight: bold;
  }

  .rejected {
    color: red;
    font-weight: bold;
  }

  .expanded-details {
    background-color: #f5f5f5;
    padding: 20px;
  }

  .detail-content {
    display: flex;
    justify-content: space-between;
  }

  .detail-section {
    flex: 1;
    margin-right: 15px;
  }

  .detail-section h4 {
    margin-bottom: 10px;
    color: #4285f4;
  }

  .no-records {
    text-align: center;
    color: #888;
    padding: 20px;
  }


/* Error message styling */
.error-message {
    color: #d93025;
    font-size: 12px;
    margin-top: 5px;
}

.tab.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  /* pointer-events handled inline */
}

/* Optional: Change color for better visibility */
.tab.disabled {
  color: #999;
  background-color: #f5f5f5;
}

.tab {
  position: relative;
}

.tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  z-index: 100;
  margin-top: 5px;
}

.tooltip:before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent #333 transparent;
}

.tabs-container {
  position: relative;
  margin-bottom: 1rem;
}

.status-message {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 10;
  padding: 0.75rem 1rem;
  background-color: #e7f3ff;
  border: 1px solid #b8daff;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-message .info-icon {
  margin-right: 0.5rem;
  font-size: 1.1em;
}

.close-btn {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 1.2em;
  cursor: pointer;
  color: #6c757d;
  padding: 0 0 0 0.5rem;
}

.close-btn:hover {
  color: #495057;
}

.disabled-field {
  opacity: 0.6;
  pointer-events: none;
}

.backup-info-textarea {
  min-height: 100px;
  padding: 8px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #ced4da;
  resize: vertical;
}

/* Enhance backup info textarea */
.backup-info-textarea {
  min-height: 100px;
  padding: 8px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #ced4da;
  resize: vertical;
}

/* Conditional field styling */
.wfh-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.wfh-hidden {
  display: none !important;
}

/* Full Page Loader Styles */
/* Loading styles removed as per user request */

/* Important: Add this to src/styles.css */
.custom-success-snackbar {
  background: #d9edf7 !important;
  color: #31708f !important;
  border: 1px solid #bce8f1 !important;
  border-radius: 4px !important;
  min-width: 300px !important;
  max-width: 80vw !important;
  padding: 15px 20px !important;
  margin: 10px !important;
  box-shadow: 0 3px 5px rgba(0,0,0,0.2) !important;
}

.custom-success-snackbar .mdc-snackbar__surface {
  background: transparent !important;
  padding: 0 !important;
  box-shadow: none !important;
}

.custom-success-snackbar .mdc-snackbar__label {
  color: #31708f !important;
  padding: 0 !important;
}

.custom-success-snackbar .mat-mdc-snack-bar-action {
  color: #31708f !important;
}

/* Error variant */
.custom-error-snackbar {
  background: #f2dede !important;
  color: #a94442 !important;
  border: 1px solid #ebccd1 !important;
  border-radius: 4px !important;
  min-width: 300px !important;
  max-width: 80vw !important;
  padding: 15px 20px !important;
  margin: 10px !important;
  box-shadow: 0 3px 5px rgba(0,0,0,0.2) !important;
}

.custom-error-snackbar .mdc-snackbar__surface {
  background: transparent !important;
  padding: 0 !important;
  box-shadow: none !important;
}

.custom-error-snackbar .mdc-snackbar__label {
  color: #a94442 !important;
  padding: 0 !important;
}

.custom-error-snackbar .mat-mdc-snack-bar-action {
  color: #a94442 !important;
}

.loader-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
  max-width: 300px;
}

.loader-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Leave balance styles */
.leave-balance-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.leave-card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.leave-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.leave-card-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.leave-card-days {
  color: #3f51b5;
  font-size: 24px;
  font-weight: 500;
}

/* History table styling */
.leave-history-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.leave-history-table th {
  background-color: #f5f5f5;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #555;
  border-bottom: 2px solid #ddd;
}

.leave-history-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  color: #333;
}

.leave-history-table tr:hover {
  background-color: #f9f9f9;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-approved {
  background-color: #e6f7ee;
  color: #00a854;
}

.status-pending {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-rejected {
  background-color: #fff1f0;
  color: #f5222d;
}

/* Pagination styling */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-controls button {
  min-width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 4px;
  background-color: #f5f5f5;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.pagination-controls button:hover:not([disabled]) {
  background-color: #e0e0e0;
}

.pagination-controls button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-controls span {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.pagination-controls span:hover {
  background-color: #e0e0e0;
}

.pagination-controls span.active {
  background-color: #3f51b5;
  color: white;
}

.showing-text {
  color: #666;
  font-size: 14px;
}

/* Loading blur styles removed as per user request */

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        margin: 0;
        border-radius: 0;
        padding: 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .buttons {
        flex-direction: column-reverse;
    }

    .btn {
        width: 100%;
    }
}

.upload-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.upload-input {
  padding: 8px;
  margin-top: 10px;
  border-radius: 8px;
  border: 1px solid #ccc;
}

.upload-btn {
  margin-top: 10px;
  padding: 8px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.upload-btn:hover {
  background-color: #357ABD;
}

/* WFH Disclaimer Styles */
.wfh-disclaimer {
  margin: 20px 0;
}

.disclaimer-card {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disclaimer-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.disclaimer-title {
  font-weight: 600;
  color: #856404;
  font-size: 16px;
}

.disclaimer-message {
  color: #856404;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.disclaimer-card mat-icon {
  color: #f39c12;
}

/* Holiday Management Styles */
.holiday-upload-section {
  margin-bottom: 30px;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 15px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filename {
  font-size: 14px;
  color: #666;
  margin-left: 10px;
}

.csv-format-info {
  background-color: #e3f2fd;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.csv-format-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #333;
}

.search-container {
  margin-bottom: 20px;
}

.search-field {
  width: 100%;
  max-width: 300px;
}

.table-responsive {
  overflow-x: auto;
}

.holiday-type-google {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.holiday-type-custom {
  background-color: #e8f5e9;
  color: #388e3c;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.no-actions {
  color: #999;
}

/* Responsive adjustments for holiday management */
@media (max-width: 768px) {
  .upload-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .filename {
    margin-left: 0;
    margin-top: 10px;
  }
}
