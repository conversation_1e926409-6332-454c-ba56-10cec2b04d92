<div class="form-container">
  <app-page-header title="Attendance and Leave/Work from Home Application"
    subtitle="Apply for leave and work from home">
  </app-page-header>

  <!-- Tabs Section -->
  <div class="tabs">

    <div class="tab" [class.active]="activeTab === 'attendance'" (click)="setActiveTab('attendance')">
      Attendance
    </div>

    <div class="tab" [class.active]="activeTab === 'new'" (click)="setActiveTab('new')">
      New Request
    </div>

    <div class="tab" [class.active]="activeTab === 'balance'" (click)="setActiveTab('balance')">
      Balance
    </div>

    <div class="tab" [class.active]="activeTab === 'history'" (click)="setActiveTab('history')">
      History
    </div>

    <div class="tab" [class.active]="activeTab === 'requests'" (click)="setActiveTab('requests')"
      *ngIf="isRoleAllowed()">
      Requests
    </div>
    
    <div class="tab" [class.active]="activeTab === 'holidays'" (click)="setActiveTab('holidays')">
        <img src="assets/google-tile.svg" alt="Google" style="width: 20px; height: 20px; margin-right: 8px; vertical-align: middle;">
      Google Holidays
    </div>
    <!-- *ngIf="isUploadAllowed()" -->
  </div>

  <app-attendance [hidden]="activeTab !== 'attendance'"></app-attendance>

  <app-balance *ngIf="activeTab === 'balance'" [leaveBalance]="leaveBalance" [canUpload]="isRoleAllowed()"
    [userRole]="userRole">
  </app-balance>

  <app-history *ngIf="activeTab === 'history'" [userRole]="userRole" [ldap]="leaveForm.get('ldap')?.value">
  </app-history>

  <app-requests *ngIf="activeTab === 'requests'"></app-requests>

  <!-- Holiday Management Tab Content -->
  <div *ngIf="activeTab === 'holidays'" class="tab-content">
    <!-- Holiday Management Section -->
<h2 style="display: flex; align-items: center; gap: 8px; font-size: 36px; font-weight: 500;">
  <img src="https://www.google.com/images/branding/googlelogo/2x/googlelogo_color_92x30dp.png"
       alt="Google"
       style="height: 1em;">
  Holidays
</h2>


    <!-- Holiday CSV Upload Section (Admin Only) -->
    <div *ngIf="canUploadHolidays" class="holiday-upload-section">
      <h3>Upload Holiday CSV</h3>
      <div class="upload-container">
      <input #holidayFileInput type="file" id="holidayFileInput" style="display: none" accept=".csv" (change)="onHolidayFileSelected($event)" />
        <button mat-raised-button color="primary" class="action-button" (click)="triggerHolidayFileInput()">
          <mat-icon>upload</mat-icon> Choose Holiday CSV
        </button>

        <button *ngIf="selectedHolidayFile" mat-raised-button color="accent" class="action-button"
                (click)="uploadHolidayCSV()" [disabled]="isUploadingHolidays">
          <mat-icon>cloud_upload</mat-icon>
          {{ isUploadingHolidays ? 'Uploading...' : 'Upload Holidays' }}
        </button>

        <button mat-raised-button color="warn" class="action-button" (click)="downloadSampleHolidayCSV()">
          <mat-icon>download</mat-icon> Download Sample CSV
        </button>

        <div *ngIf="selectedHolidayFile" class="filename">
          Selected File: {{ selectedHolidayFile.name }}
        </div>
      </div>

      <div class="csv-format-info">
        <p><strong>CSV Format:</strong> Date,Holiday,Description</p>
        <p><strong>Date Format:</strong> M/d/yyyy (e.g., 1/1/2025, 12/25/2025)</p>
        <p><strong>Note:</strong> Uploading a new CSV will replace all existing Google holidays.</p>
      </div>
      <div class="divider"></div>
    </div>

    <!-- Holiday List Section -->
    <div class="holiday-list-section">
      <div class="search-container">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search Holidays</mat-label>
          <mat-icon matPrefix>search</mat-icon>
          <input matInput (keyup)="applyHolidayFilter($event)" placeholder="Search holidays">
        </mat-form-field>
      </div>

      <div class="table-responsive">
        <mat-table [dataSource]="holidayDataSource" matSort class="mat-elevation-z8">
          <!-- Holiday Date Column -->
          <ng-container matColumnDef="holidayDate">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Date</mat-header-cell>
            <mat-cell *matCellDef="let holiday">{{ holiday.holidayDate | date: 'MMM dd, yyyy' }}</mat-cell>
          </ng-container>

          <!-- Holiday Name Column -->
          <ng-container matColumnDef="holidayName">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Holiday Name</mat-header-cell>
            <mat-cell *matCellDef="let holiday">{{ holiday.holidayName }}</mat-cell>
          </ng-container>

          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <mat-header-cell *matHeaderCellDef>Description</mat-header-cell>
            <mat-cell *matCellDef="let holiday">{{ holiday.description || 'No description' }}</mat-cell>
          </ng-container>

          <!-- Holiday Type Column -->
          <ng-container matColumnDef="holidayType">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Type</mat-header-cell>
            <mat-cell *matCellDef="let holiday">
              <span *ngIf="holiday.holidayType === 'GOOGLE'" class="holiday-type-google">
                <img src="https://www.google.com/images/branding/googlelogo/2x/googlelogo_color_92x30dp.png" alt="Google" style="width: 60px; height: 18px; vertical-align: middle;">
              </span>
              <span *ngIf="holiday.holidayType === 'CUSTOM'" class="holiday-type-custom">
                {{ holiday.holidayType }}
              </span>
            </mat-cell>
          </ng-container>

          <!-- Uploaded By Column -->
          <ng-container matColumnDef="uploadedBy">
            <mat-header-cell *matHeaderCellDef>Uploaded By</mat-header-cell>
            <mat-cell *matCellDef="let holiday">{{ holiday.uploadedBy || 'System' }}</mat-cell>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
            <mat-cell *matCellDef="let holiday">
              <button mat-icon-button color="warn"
                      *ngIf="canUploadHolidays && holiday.holidayType === 'CUSTOM'"
                      (click)="deleteHoliday(holiday)"
                      matTooltip="Delete Holiday">
                <mat-icon>delete</mat-icon>
              </button>
              <span *ngIf="!canUploadHolidays || holiday.holidayType === 'GOOGLE'"
                    class="no-actions">-</span>
            </mat-cell>
          </ng-container>

          <mat-header-row *matHeaderRowDef="holidayDisplayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: holidayDisplayedColumns;"></mat-row>
        </mat-table>

        <mat-paginator #holidayPaginator
          [length]="holidayDataSource.data.length"
          [pageSize]="10"
          [pageSizeOptions]="[5, 10, 25, 50]"
          showFirstLastButtons>
        </mat-paginator>
      </div>
    </div>
  </div>

  <!-- New Request Tab Content -->
  <form [formGroup]="leaveForm" (ngSubmit)="onSubmit()" *ngIf="activeTab === 'new'" class="tab-content">
    <h2>Apply for Leave/WFH</h2>

    <!-- Common Fields -->
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Ldap</mat-label>
        <input matInput formControlName="ldap" (blur)="fetchManager()" readonly>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Approving Lead/Manager</mat-label>
        <input matInput formControlName="approver" readonly>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Request Type</mat-label>
        <mat-select formControlName="requestType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of requestTypeOptions" [value]="option.value">{{option.label}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field *ngIf="!isWFH" appearance="outline" class="full-width">
        <mat-label>Leave Type</mat-label>
        <mat-select formControlName="leaveType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of leaveTypeOptions" [value]="option.value">{{option.label}}</mat-option>
        </mat-select>
      </mat-form-field>
      

      <div *ngIf="isWFH">
        <div class="form-row full-width">
          <label>Attach Supporting Document (Optional - [pdf,png,jpg,jpeg-Accepted])</label>
          <input type="file" accept=".pdf,.png,.jpg,.jpeg" (change)="onFileSelected($event)" />
        </div>
      </div>
    </div>

    <div class="form-row full-width">
      <mat-form-field appearance="outline" class="full-width-field">
        <mat-label>Duration</mat-label>
        <mat-select formControlName="durationType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of durationTypeOptions" [value]="option.value">{{option.label}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div *ngIf="!isWFH">
      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>OOO Screenshot Link</mat-label>
          <input matInput formControlName="oooProof">
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Timesheet Screenshot Link</mat-label>
          <input matInput formControlName="timesheetProof">
        </mat-form-field>
      </div>

      <div class="form-row full-width">
        <mat-form-field appearance="outline" class="full-width-field">
          <mat-label>Backup Info</mat-label>
          <textarea matInput formControlName="backupInfo" rows="4"></textarea>
        </mat-form-field>
      </div>
    </div>

    <!-- Common Fields -->

    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Start Date</mat-label>
        <input matInput [matDatepicker]="startDatePicker" formControlName="startDate" readonly>
        <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #startDatePicker></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>End Date</mat-label>
        <input matInput [matDatepicker]="endDatePicker" formControlName="endDate" readonly [disabled]="isEndDateDisabled">
        <mat-datepicker-toggle matSuffix [for]="endDatePicker" [disabled]="isEndDateDisabled"></mat-datepicker-toggle>
        <mat-datepicker #endDatePicker [disabled]="isEndDateDisabled"></mat-datepicker>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Start Time</mat-label>
        <input matInput type="time" formControlName="startTime">
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>End Time</mat-label>
        <input matInput type="time" formControlName="endTime">
      </mat-form-field>
    </div>

    <div *ngIf="isWFH" class="form-row full-width">
      <mat-form-field appearance="outline" class="full-width-field">
        <mat-label>Reason for Work From Home</mat-label>
        <textarea matInput formControlName="reason" rows="4"
          placeholder="Enter the reason for Work From Home"></textarea>
      </mat-form-field>
    </div>

    <!-- WFH Disclaimer -->
    <div *ngIf="showWFHDisclaimer" class="wfh-disclaimer">
      <mat-card class="disclaimer-card">
        <mat-card-content>
          <div class="disclaimer-header">
            <mat-icon color="warn">warning</mat-icon>
            <span class="disclaimer-title">Important Notice</span>
          </div>
          <p class="disclaimer-message">{{ wfhDisclaimerMessage }}</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Buttons -->
    <div class="buttons">
      <button mat-raised-button color="warn" class="action-button cancel-button" type="button"
        (click)="resetForm()">Cancel</button>
      <button mat-raised-button color="primary" class="action-button save-button" type="submit">Submit Request</button>
    </div>
  </form>
</div>