import { Component, Input, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { FormControl, FormGroup } from '@angular/forms';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { LeaveService } from 'src/app/services/leave.service';
import { NotificationService } from 'src/app/shared/notification.service';
import { ConfirmationDialogComponent } from 'src/app/confirm-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from "@angular/material/icon";
import { EditLeaveRequestComponent } from './edit-leave-request/edit-leave-request.component';
import { DocumentPreviewDialogComponent } from '../requests/document-preview-dialog/document-preview-dialog.component';

@Component({
  selector: 'app-history',
  templateUrl: './history.component.html',
  styleUrls: ['./history.component.css'],
})
export class HistoryComponent implements OnInit {
  @Input() ldap!: string;
  @Input() leaveHistory: any[] = [];
  @Input() userRole?: string;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(private leaveService: LeaveService, private notificationService: NotificationService,
    private dialog: MatDialog
  ) { }

  dataSource = new MatTableDataSource<any>();
  displayedColumns: string[] = [
    'timestamp',
    'ldap', 
    'applicationType',
    'leaveType',
    'duration',
    'startDate', 
    'startTime', 
    'endDate', 
    'endTime',
    'shiftCodeAtRequestTime', 
    'timesheetProof',
    'oooProof',
    'documentPath',
    'backupInfo',
    'reason',
    'status', 
    'actions'
  ];  allColumns: string[] = [...this.displayedColumns];

  columnDisplayNames: { [key: string]: string } = {
    timestamp:'Requested On',
    ldap: 'LDAP',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    startTime : 'Start Time',
    endDate: 'End Date',
    endTime: 'End Time',
    applicationType: 'Application Type',
    duration: 'Duration',
    shiftCodeAtRequestTime : 'Shift',
    timesheetProof : 'TimeSheet SS',
    oooProof : 'OOO SS',
    reason : 'Reason',
    documentPath: 'Document',
    status: 'Status',
    backupInfo: 'Backup Info',
    actions: 'Actions'
  };


  columnSearchText = '';
  showColumnFilters = false;

  filterValues: any = {};
  dateRange = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null)
  });

  ngOnInit(): void {
    this.fetchLeaveHistory(this.ldap);
  }

  ngAfterViewInit() {
    if (this.paginator) {
      this.dataSource.paginator = this.paginator;
    }
    if (this.sort) {
      this.dataSource.sort = this.sort;
    }
  }

  openPreviewDialog(row: any): void {
    const baseUrlProd = 'https://teamsphere.in/'; //URL for Development Purposes

    const baseUrl = 'http://localhost:8080/'; // Use environment-specific base URL
  
    const documentPath = row.documentPath || row.document || null;
  
    this.dialog.open(DocumentPreviewDialogComponent, {
      width: '600px',
      data: {
        reason: row.reason,
        documentPath: documentPath ? baseUrl + documentPath : null
      }
    });
  }

  ngOnChanges(): void {
    if (this.ldap) {
      this.fetchLeaveHistory(this.ldap);
    }
  }

  private formatTimestamp(timestamp: string): string {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}


  fetchLeaveHistory(ldap: string): void {
    console.log("Inside the history component");

    this.leaveService.getUserLeaveHistory(ldap).subscribe({
      next: (data: any[]) => {
        console.log("Data Of history ",data)
        this.leaveHistory = data.map(item => ({
          id: item.id,
          timestamp: this.formatTimestamp(item.timestamp),
          ldap: ldap,
          status: item.status,
          leaveType: item.leaveType,
          applicationType: item.applicationType,
          startDate: item.startDate ? new Date(item.startDate) : null,
          startTime: item.startTime || '',
          endDate: item.endDate ? new Date(item.endDate) : null,
          endTime: item.endTime || '',
          shiftCodeAtRequestTime: item.shiftCodeAtRequestTime || '',
          duration: item.duration,
          approver: item.approver,
          reason: item.reason,
          timesheetProof: item.timesheetProof,
          oooProof: item.oooProof,
          documentPath: item.documentPath,
          backupInfo: item.backupInfo,
        }));

        this.dataSource.data = this.leaveHistory;

        if (this.sort) {
          this.dataSource.sort = this.sort;
        }
        if (this.paginator) {
          this.paginator.firstPage();
        }
      },
      error: (error) => {
        console.error('Error fetching leave history:', error);
      }
    });
  }


  applyGlobalFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
    this.dataSource.filter = filterValue;
  }

  applyDateFilter(): void {
    const start = this.dateRange.get('start')?.value;
    const end = this.dateRange.get('end')?.value;
    this.dataSource.data = this.leaveHistory.filter(entry => {
      const entryDate = new Date(entry.startDate);
      return (!start || entryDate >= start) && (!end || entryDate <= end);
    });
  }

  toggleColumnFilters(): void {
    this.showColumnFilters = !this.showColumnFilters;
  }

  toggleAllColumns(checked: boolean): void {
    this.displayedColumns = checked ? [...this.allColumns] : [];
  }

  isColumnDisplayed(column: string): boolean {
    return this.displayedColumns.includes(column);
  }

  toggleColumn(column: string): void {
    const index = this.displayedColumns.indexOf(column);
    if (index >= 0) {
      this.displayedColumns.splice(index, 1);
    } else {
      this.displayedColumns.push(column);
    }
  }

  getFilteredColumns(): string[] {
    if (!this.columnSearchText.trim()) return this.allColumns;
    const searchLower = this.columnSearchText.toLowerCase();
    return this.allColumns.filter(column =>
      this.columnDisplayNames[column].toLowerCase().includes(searchLower)
    );
  }

  downloadCSV(): void {
    const exportData = this.dataSource.data.map(item => ({
      LDAP: item.ldap,
      'Leave Type': item.leaveType,
      'Start Date': item.startDate,
      'End Date': item.endDate,
      Reason: item.reason,
      Status: item.status,
      Comment: item.comment
    }));

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Leave History');

    const excelBuffer: any = XLSX.write(workbook, { bookType: 'csv', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `leave_history_${new Date().toISOString().split('T')[0]}.csv`);
  }

  editRequest(entry: any): void {
    console.log("Entry ", entry);
    if (entry.status !== 'PENDING') return;
  
    const dialogRef = this.dialog.open(EditLeaveRequestComponent, {
      width: '700px',
      data: {
        id: entry.id,
        ldap: entry.ldap,
        approver: entry.approver,
        status: entry.status,
        applicationType: entry.applicationType,
        duration: entry.duration,
        leaveType: entry.leaveType,
        startDate: entry.startDate,
        endDate: entry.endDate,
        startTime: entry.startTime,
        endTime: entry.endTime,
        reason: entry.reason,
        timesheetProof: entry.timesheetProof,
        oooProof: entry.oooProof,
        backupInfo: entry.backupInfo,
        documentPath: entry.documentPath // for WFH
      }
    });
  
    dialogRef.afterClosed().subscribe(updatedData => {
      if (updatedData) {
        const updatedRequest = { ...entry, ...updatedData };
  
        this.leaveService.updateLeaveRequest(updatedRequest).subscribe({
          next: () => {
            this.notificationService.showNotification({
              type: 'success',
              message: 'Request updated successfully.'
            });
            this.fetchLeaveHistory(this.ldap);
          },
          error: () => {
            this.notificationService.showNotification({
              type: 'error',
              message: 'Failed to update leave request.'
            });
          }
        });
      }
    });
  }
  
  deleteRequest(entry: any): void {
    if (entry.status !== 'PENDING') return;

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Deletion',
        message: `Are you sure you want to delete request from ${entry.startDate.toDateString()} to ${entry.endDate.toDateString()}?`,
        color: 'warn',
        confirmButtonText: 'Delete'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.leaveService.deleteLeaveRequestById(entry.id).subscribe({
          next: () => {
            this.notificationService.showNotification({
              type: 'success',
              message: 'Leave request deleted successfully.'
            });
            this.fetchLeaveHistory(this.ldap); // Refresh list
          },
          error: () => {
            this.notificationService.showNotification({
              type: 'error',
              message: 'Failed to delete leave request.'
            });
          }
        });
      }
    });
  }


  get allColumnsSelected(): boolean {
    return this.displayedColumns.length === this.allColumns.length;
  }
}
