import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { LeaveService } from 'src/app/services/leave.service';
import { NotificationService } from 'src/app/shared/notification.service';
import { MatDialog } from '@angular/material/dialog';
import { DocumentPreviewDialogComponent } from './document-preview-dialog/document-preview-dialog.component';
import { MatButtonToggleChange } from '@angular/material/button-toggle';
import * as Papa from 'papaparse';
import { FormGroup, FormControl } from '@angular/forms';

@Component({
  selector: 'app-requests',
  templateUrl: './requests.component.html',
  styleUrls: ['./requests.component.css']
})
export class RequestsComponent implements OnInit {
  displayedColumns: string[] = [
    'timestamp', 'ldap', 'name', 'requestType', 'leaveType',
    'startDate', 'endDate', 'duration', 'status', 'reason', 'document', 'actions'
  ];
  columnDisplayNames: Record<string, string> = {
    timestamp: 'Requested On',
    ldap: 'LDAP',
    name: 'Name',
    requestType: 'Request Type',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    duration: 'Duration',
    status: 'Status',
    reason: 'Reason',
    document: 'Document',
    actions: 'Actions'
  };

  columnSearchText: string = '';
  allColumnsSelected: boolean = false;
  showColumnToggle: boolean = false;
  showColumnFilters = false;
  userRole: string = '';
  userName: string = '';
  processedDataSource: MatTableDataSource<any> = new MatTableDataSource();
  processedRequests: any[] = []; // Raw data

  currentFilterMenuState: {
    columnKey: string | null,
    searchText: string,
    tempSelected: Set<any>
  } = { columnKey: null, searchText: '', tempSelected: new Set() };

  filteredMenuOptions: any[] = [];
  appliedFilters: Record<string, Set<any>> = {};

  lastAppliedStartDate?: string;
  lastAppliedEndDate?: string;

  // if you have a toggle for direct reports
  showMyDirectReports: boolean = true;

  // Tab DataSources
  pendingDataSource: MatTableDataSource<any> = new MatTableDataSource();
  processedApprovedDataSource = new MatTableDataSource<any>();
  processedRejectedDataSource = new MatTableDataSource<any>();
  processedRevokedDataSource = new MatTableDataSource<any>();

  // Filters
  processedStatuses: string[] = ['APPROVED', 'REJECTED', 'REVOKED'];
  selectedStatus: string = '';

  dateRange = new FormGroup({
    start: new FormControl<Date | null>(new Date()),
    end: new FormControl<Date | null>(new Date())
  });

  // ViewChilds
  @ViewChild('pendingPaginator') pendingPaginator!: MatPaginator;
  @ViewChild('processedPaginator') processedPaginator!: MatPaginator;
  @ViewChild('approvedPaginator') approvedPaginator!: MatPaginator;
  @ViewChild('rejectedPaginator') rejectedPaginator!: MatPaginator;
  @ViewChild('revokedPaginator') revokedPaginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private leaveService: LeaveService,
    private notificationService: NotificationService,
    private dialog: MatDialog,
  ) { }

  ngOnInit(): void {
    this.userRole = (localStorage.getItem('role') || '').toUpperCase();
    this.userName = (localStorage.getItem('username') || '').toLowerCase();
    this.fetchPendingRequests();
    this.fetchProcessedRequests();
  }

  isLeadOrManager(): boolean {
    return this.userRole === 'LEAD' || this.userRole === 'MANAGER';
  }

  toggleOnBehalfMode(isChecked: boolean) {
    this.showMyDirectReports = isChecked;
    console.log('Request Toggle changed:', isChecked);

    // 🔄 Refresh data with current date filter
    this.fetchPendingRequests(true);
    this.fetchProcessedRequests(true);
  }

  ngAfterViewInit(): void {
    if (this.processedApprovedDataSource) {
      this.processedApprovedDataSource.paginator = this.approvedPaginator;
      this.processedApprovedDataSource.sort = this.sort;
    }
    if (this.processedRejectedDataSource) {
      this.processedRejectedDataSource.paginator = this.rejectedPaginator;
      this.processedRejectedDataSource.sort = this.sort;
    }
    if (this.processedRevokedDataSource) {
      this.processedRevokedDataSource.paginator = this.revokedPaginator;
      this.processedRevokedDataSource.sort = this.sort;
    }

    this.pendingDataSource.paginator = this.pendingPaginator;
    this.pendingDataSource.sort = this.sort;
  }

  openPreviewDialog(row: any): void {
    const baseUrl = 'https://teamsphere.in/'; // Use for Main Production

    // const baseUrl = 'http://localhost:8080/';  URL for Development Purposes

    const documentPath = row.documentPath || row.document || null;

    this.dialog.open(DocumentPreviewDialogComponent, {
      width: '600px',
      data: {
        reason: row.reason,
        documentPath: documentPath ? baseUrl + documentPath : null
      }
    });
  }

  getLastWeekStartDate(): Date {
    const date = new Date();
    date.setDate(date.getDate() - 7);
    return date;
  }

  fetchPendingRequests(isFilter: boolean = false): void {
    const selectedStart = this.dateRange?.get('start')?.value;
    const selectedEnd = this.dateRange?.get('end')?.value;

    let startDate: string;
    let endDate: string;

    const today = new Date();

    if (isFilter && selectedStart && selectedEnd) {
      // User explicitly applied a filter
      startDate = this.formatDate(selectedStart);
      endDate = this.formatDate(selectedEnd);

      this.lastAppliedStartDate = startDate;
      this.lastAppliedEndDate = endDate;
    } else if (this.lastAppliedStartDate && this.lastAppliedEndDate) {
      // Reuse last filter if exists
      startDate = this.lastAppliedStartDate;
      endDate = this.lastAppliedEndDate;
    } else if (
      this.userRole === 'LEAD' ||
      this.userRole === 'MANAGER' ||
      this.userRole === 'ADMIN_OPS_MANAGER'
    ) {
      // Default for leads/managers/admins → today
      startDate = this.formatDate(today);
      endDate = this.formatDate(today);
    } else {
      // Default for USER (normal employee) → past week
      endDate = this.formatDate(today);
      const pastWeek = new Date(today);
      pastWeek.setDate(today.getDate() - 6);
      startDate = this.formatDate(pastWeek);
    }

    this.leaveService.getPendingRequestsForLead(startDate, endDate, this.showMyDirectReports).subscribe({
      next: (requests) => {
        this.pendingDataSource = new MatTableDataSource(requests);
        this.pendingDataSource.paginator = this.pendingPaginator;
        this.pendingDataSource.sort = this.sort;
      },
      error: () => {
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to fetch pending requests.'
        });
      }
    });
  }

  private formatDate(date: Date): string {
    // Convert to local date only (ignore timezone shift)
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  canApprove(request: any): boolean {
    const isLead = this.userRole === 'LEAD';
    const isWFH = request.applicationType === 'Work From Home';
    const isDsheoran = this.userName === 'dsheoran';

    // Compute duration in days using start and end dates
    const start = new Date(request.startDate);
    const end = new Date(request.endDate);
    const durationInDays = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    const isLongWFH = durationInDays >= 3;

    // Leads cannot approve any WFH
    if (isLead && isWFH) return false;

    // Only dsheoran can approve WFH of 3+ days
    if (isWFH && isLongWFH && !isDsheoran) return false;

    return true;
  }

  fetchProcessedRequests(isFilter: boolean = false): void {
    const selectedStart = this.dateRange?.get('start')?.value;
    const selectedEnd = this.dateRange?.get('end')?.value;

    let startDate: string;
    let endDate: string;

    const today = new Date();

    if (isFilter && selectedStart && selectedEnd) {
      startDate = this.formatDate(selectedStart);
      endDate = this.formatDate(selectedEnd);

      this.lastAppliedStartDate = startDate;
      this.lastAppliedEndDate = endDate;
    } else if (this.lastAppliedStartDate && this.lastAppliedEndDate) {
      startDate = this.lastAppliedStartDate;
      endDate = this.lastAppliedEndDate;
    } else if (
      this.userRole === 'LEAD' ||
      this.userRole === 'MANAGER' ||
      this.userRole === 'ADMIN_OPS_MANAGER'
    ) {
      startDate = this.formatDate(today);
      endDate = this.formatDate(today);
    } else {
      endDate = this.formatDate(today);
      const pastWeek = new Date(today);
      pastWeek.setDate(today.getDate() - 6);
      startDate = this.formatDate(pastWeek);
    }

    this.leaveService.getProcessedRequestsForLead(startDate, endDate, this.showMyDirectReports).subscribe({
      next: (requests) => {
        this.processedRequests = requests || [];
        this.processedApprovedDataSource.data = this.processedRequests.filter(r => r.status === 'APPROVED');
        this.processedRejectedDataSource.data = this.processedRequests.filter(r => r.status === 'REJECTED');
        this.processedRevokedDataSource.data = this.processedRequests.filter(r => r.status === 'REVOKED');

        this.selectedStatus = 'APPROVED';
        setTimeout(() => this.updatePaginatorAndSort(), 0);
      },
      error: () => {
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to fetch processed requests.'
        });
      }
    });
  }

  openFilterMenu(columnKey: string, trigger: any) {
    this.currentFilterMenuState = {
      columnKey,
      searchText: '',
      tempSelected: new Set(this.appliedFilters[columnKey] || [])
    };
    this.filteredMenuOptions = this.getUniqueColumnValues(columnKey);
  }

  isFilterActive(columnKey: string): boolean {
    return !!(this.appliedFilters[columnKey] && this.appliedFilters[columnKey].size > 0);
  }

  // Get unique values for a column
  getUniqueColumnValues(columnKey: string): any[] {
    let data = [...this.getProcessedDataSource().data, ...this.pendingDataSource.data];
    const values = Array.from(new Set(data.map(row => row[columnKey] || 'NA')));
    return values.filter(v =>
      v.toString().toLowerCase().includes(this.currentFilterMenuState.searchText.toLowerCase())
    );
  }

  toggleTempSelection(value: any, checked: boolean) {
    if (checked) this.currentFilterMenuState.tempSelected.add(value);
    else this.currentFilterMenuState.tempSelected.delete(value);
  }

  // Check if value is temp selected
  isTempSelected(value: any): boolean {
    return this.currentFilterMenuState.tempSelected.has(value);
  }

  downloadCSV(type: 'processed' | 'pending') {
    let dataSource;

    if (type === 'processed') {
      dataSource = this.getProcessedDataSource(); // ✅ use selectedStatus table
    } else {
      dataSource = this.pendingDataSource;
    }

    const csv = Papa.unparse(dataSource.filteredData || dataSource.data);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);

    // Dynamic filename
    const fileName =
      type === 'processed'
        ? `processed_requests_${this.selectedStatus.toLowerCase()}.csv`
        : 'pending_requests.csv';

    link.setAttribute('download', fileName);
    link.click();
  }

  getFilteredColumns(): string[] {
    if (!this.columnSearchText) {
      return this.displayedColumns;
    }
    return this.displayedColumns.filter(col =>
      this.columnDisplayNames[col]?.toLowerCase().includes(this.columnSearchText.toLowerCase())
    );
  }

  isColumnDisplayed(column: string): boolean {
    return this.displayedColumns.includes(column);
  }

  toggleColumn(column: string) {
    if (this.displayedColumns.includes(column)) {
      this.displayedColumns = this.displayedColumns.filter(col => col !== column);
    } else {
      this.displayedColumns.push(column);
    }
  }

  toggleAllColumns(checked: boolean) {
    if (checked) {
      this.displayedColumns = Object.keys(this.columnDisplayNames);
    } else {
      this.displayedColumns = [];
    }
    this.allColumnsSelected = checked;
  }

  toggleSelectAllTemp(checked: boolean) {
    if (checked) {
      this.getUniqueColumnValues(this.currentFilterMenuState.columnKey!)
        .forEach(v => this.currentFilterMenuState.tempSelected.add(v));
    } else {
      this.currentFilterMenuState.tempSelected.clear();
    }
  }

  isAllTempSelected(): boolean {
    const all = this.getUniqueColumnValues(this.currentFilterMenuState.columnKey!);
    return all.length > 0 && all.every(v => this.currentFilterMenuState.tempSelected.has(v));
  }

  isSomeTempSelected(): boolean {
    const all = this.getUniqueColumnValues(this.currentFilterMenuState.columnKey!);
    return this.currentFilterMenuState.tempSelected.size > 0 && !this.isAllTempSelected();
  }

  // Apply filter
  onFilterApplied() {
    if (this.currentFilterMenuState.columnKey) {
      this.appliedFilters[this.currentFilterMenuState.columnKey] =
        new Set(this.currentFilterMenuState.tempSelected);
    }
    this.applyFilters();
  }

  // Clear filter
  clearColumnFilter() {
    if (this.currentFilterMenuState.columnKey) {
      delete this.appliedFilters[this.currentFilterMenuState.columnKey];
    }
    this.applyFilters();
  }

  // Apply filters across datasources
  applyFilters() {
    const filterFn = (data: any) => {
      return Object.keys(this.appliedFilters).every(col => {
        const allowed = this.appliedFilters[col];
        return allowed.size === 0 || allowed.has(data[col] || 'NA');
      });
    };

    this.getProcessedDataSource().filterPredicate = filterFn;
    this.getProcessedDataSource().filter = Math.random().toString();

    this.pendingDataSource.filterPredicate = filterFn;
    this.pendingDataSource.filter = Math.random().toString();
  }

  updatePaginatorAndSort(): void {
    switch (this.selectedStatus) {
      case 'APPROVED':
        this.processedApprovedDataSource.paginator = this.approvedPaginator;
        this.processedApprovedDataSource.sort = this.sort;
        break;
      case 'REJECTED':
        this.processedRejectedDataSource.paginator = this.rejectedPaginator;
        this.processedRejectedDataSource.sort = this.sort;
        break;
      case 'REVOKED':
        this.processedRevokedDataSource.paginator = this.revokedPaginator;
        this.processedRevokedDataSource.sort = this.sort;
        break;
    }
  }

  getProcessedDataSource(): MatTableDataSource<any> {
    switch (this.selectedStatus) {
      case 'APPROVED': return this.processedApprovedDataSource;
      case 'REJECTED': return this.processedRejectedDataSource;
      case 'REVOKED': return this.processedRevokedDataSource;
      default: return new MatTableDataSource();
    }
  }

  approveRequest(request: any): void {
    this.leaveService.approveRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request approved.'
        });
        this.fetchPendingRequests();
        this.fetchProcessedRequests(); // also update processed
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to approve request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  rejectRequest(request: any): void {
    this.leaveService.rejectRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request rejected.'
        });
        this.fetchPendingRequests();
        this.fetchProcessedRequests();
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to reject request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  revokeRequest(request: any): void {
    this.leaveService.revokeRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request revoked.'
        });
        this.fetchProcessedRequests();
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to revoke request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  applyDateFilter() {
    const { start, end } = this.dateRange.value;

    if (!start || !end) {
      // If user clears filter → reset API data to default
      this.fetchPendingRequests(false);
      this.fetchProcessedRequests(false);
      return;
    }

    // 👇 Call APIs with filter enabled
    this.fetchPendingRequests(true);
    this.fetchProcessedRequests(true);
  }


  onStatusChange(event: MatButtonToggleChange): void {
    this.selectedStatus = event.value;
    setTimeout(() => this.updatePaginatorAndSort(), 0); // fixes paginator not binding
  }

  isPastEndDate(endDate: string): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const leaveEndDate = new Date(endDate);
    leaveEndDate.setHours(0, 0, 0, 0);
    return leaveEndDate < today;
  }
}

