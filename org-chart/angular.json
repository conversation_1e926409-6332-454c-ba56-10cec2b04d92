{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"org-chart": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/org-chart", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/deeppurple-amber.css", "src/styles.css"], "scripts": ["node_modules/gojs/release/go.js"], "allowedCommonJsDependencies": ["file-saver", "highcharts"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "8mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "150kb", "maximumError": "200kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "org-chart:build:production"}, "development": {"browserTarget": "org-chart:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "org-chart:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/deeppurple-amber.css", "src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": "774ccd48-b541-4567-a3e3-e3fafb163c74"}}