package com.vbs.capsAllocation.util;

import com.vbs.capsAllocation.service.CustomUserDetailsService;
import com.vbs.capsAllocation.service.TokenBlacklistService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Enumeration;
@Component
public class JwtFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final CustomUserDetailsService userDetailsService;
    private final TokenBlacklistService tokenBlacklistService;

    public JwtFilter(JwtUtil jwtUtil, CustomUserDetailsService userDetailsService, TokenBlacklistService tokenBlacklistService) {
        this.jwtUtil = jwtUtil;
        this.userDetailsService = userDetailsService;
        this.tokenBlacklistService = tokenBlacklistService;
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        

        // Print all headers for debugging
        Enumeration<String> headerNames = request.getHeaderNames();
        System.out.println("Headers:");
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            System.out.println(headerName + ": " + request.getHeader(headerName));
        }

        String path = request.getRequestURI();

        // Skip JWT check for login and other public endpoints
        if (path.startsWith("/approveRequest") ||
            path.equals("/auth/login") ||
            path.equals("/auth/forgot-password") ||
            path.equals("/auth/verify-otp") ||
            path.equals("/auth/reset-password-with-otp")||
	path.equals("/auth/signup") ||
            path.equals("/admin/register"))
	{
            System.out.println("Skipping JWT validation for public endpoint: " + path);
            filterChain.doFilter(request, response);
            return;
        }

        // Special handling for password reset endpoint
        if (path.equals("/admin/reset-password-postman")) {
            System.out.println("Special handling for password reset endpoint");
            // Continue with JWT validation but log more details
        }

        final String authorizationHeader = request.getHeader("Authorization");
        String username = null;
        String token = null;

        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            token = authorizationHeader.substring(7);

            // Check if token is blacklisted
            if (tokenBlacklistService.isTokenBlacklisted(token)) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Token has been invalidated. Please login again.");
                return;
            }

            username = jwtUtil.extractUsername(token);
        }

        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            try {
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                if (path.equals("/admin/reset-password-postman")) {
                    System.out.println("User details for password reset: " + userDetails.getUsername());
                    System.out.println("Authorities: " + userDetails.getAuthorities());
                }

                if (jwtUtil.isTokenValid(token)) {
                    UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.getAuthorities());
                    authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);

                    if (path.equals("/admin/reset-password-postman")) {
                        System.out.println("Authentication set in SecurityContext for password reset");
                    }
                } else {
                    System.out.println("Token validation failed for user: " + username);
                }
            } catch (Exception e) {
                System.out.println("Error during authentication: " + e.getMessage());
                e.printStackTrace();
            }
        }

        filterChain.doFilter(request, response);
    }
}
