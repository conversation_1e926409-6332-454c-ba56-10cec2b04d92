package com.vbs.capsAllocation.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Entity
@Table(name = "time_entries")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TimeEntry {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    @Column(nullable = false)
    private LocalDate entryDate;

    @Column(nullable = false)
    private String ldap;

    @ManyToOne
    @JoinColumn(name = "lead_id")
    private User lead;

    @Column(nullable = false)
    private String process;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Activity activity;

    @Column(name = "time_in_mins", nullable = false)
    private Integer timeInMins;

    @Column(name = "attendance_type")
    private String attendanceType;

    private String comment;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TimeEntryStatus status = TimeEntryStatus.PENDING;

    private String rejectionComment;

    @Column(name = "is_overtime")
    private Boolean isOvertime;
}
