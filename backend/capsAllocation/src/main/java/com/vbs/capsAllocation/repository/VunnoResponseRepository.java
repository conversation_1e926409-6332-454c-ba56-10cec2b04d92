package com.vbs.capsAllocation.repository;

import com.vbs.capsAllocation.dto.VunnoRequestDto;
import com.vbs.capsAllocation.model.VunnoResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface VunnoResponseRepository extends JpaRepository<VunnoResponse, Long> {

    @Query("SELECT v FROM VunnoResponse v WHERE v.employee.ldap = :ldap")
    List<VunnoResponse> findByEmployeeLdap(@Param("ldap") String ldap);

    List<VunnoResponse> findByEmployeeLdapAndStatusNot(String ldap, String status);

    @Query("SELECT v FROM VunnoResponse v WHERE v.employee.ldap IN :ldaps AND v.status = 'PENDING'")
    List<VunnoResponse> findPendingRequestsByTeamLdaps(@Param("ldaps") List<String> ldaps);

    @Query("SELECT v FROM VunnoResponse v WHERE v.employee.ldap IN :ldaps AND v.status = 'APPROVED'")
    List<VunnoResponse> findApprovedRequestsByTeamLdaps(@Param("ldaps") List<String> ldaps);

    List<VunnoResponse> findByEmployeeLdapInAndStatusIn(List<String> ldaps, List<String> statuses);

    @Query("""
    SELECT new com.vbs.capsAllocation.dto.VunnoRequestDto(
                    r.id,
                    r.employee.ldap,
                    r.approver,
                    r.applicationType,
                    r.leaveType,
                    r.duration,
                    r.fromDate,
                    r.toDate,
                    r.backup,
                    r.orgScreenshot,
                    r.timesheetScreenshot,
                    r.status,
                    r.requestorName,
                    r.reason,
                    r.documentPath,
                    r.timestamp
                )
                FROM VunnoResponse r
                WHERE r.employee.ldap IN :employeeLdaps
                  AND r.fromDate BETWEEN :startDate AND :endDate
                  AND r.status IN :statuses
""")
    List<VunnoRequestDto> findProcessedDtos(
            @Param("employeeLdaps") List<String> employeeLdaps,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("statuses") List<String> statuses);


    @Query(value = """
        SELECT vr.id, e.ldap, vr.approver, vr.application_type, vr.leave_type, vr.duration,
               vr.from_date, vr.to_date, vr.backup, vr.org_screenshot, vr.timesheet_screenshot,
               vr.status, (e.first_name || ' ' || e.last_name), vr.reason, vr.document_path, vr.timestamp
        FROM public.vunno_responses vr
        JOIN public.employee e ON e.id = vr.employee_id
        WHERE (:ldaps IS NULL OR e.ldap = ANY(:ldaps))
          AND vr.status = ANY(:statuses)
          AND (:startDate IS NULL OR date(vr.timestamp) >= :startDate)
          AND (:endDate IS NULL OR date(vr.timestamp) <= :endDate)
        ORDER BY vr.timestamp DESC
        """, nativeQuery = true)
    List<VunnoRequestDto> findProcessedDtosNative(
            @Param("ldaps") List<String> ldaps,
            @Param("statuses") List<String> statuses,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    // Pending requests
    List<VunnoResponse> findByEmployeeLdapInAndFromDateBetweenAndStatus(
            List<String> employeeLdaps,
            LocalDate startDate,
            LocalDate endDate,
            String status);

    // Processed requests
    List<VunnoResponse> findByEmployeeLdapInAndFromDateBetweenAndStatusIn(
            List<String> employeeLdaps,
            LocalDate startDate,
            LocalDate endDate,
            List<String> statuses);

}