package com.vbs.capsAllocation.repository;

import com.vbs.capsAllocation.model.Project;
import com.vbs.capsAllocation.model.TimeEntry;
import com.vbs.capsAllocation.model.TimeEntryStatus;
import com.vbs.capsAllocation.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TimeEntryRepository extends JpaRepository<TimeEntry, Long> {
    List<TimeEntry> findByUser(User user);
    List<TimeEntry> findByProject(Project project);
    List<TimeEntry> findByLead(User lead);
    List<TimeEntry> findByStatus(TimeEntryStatus status);
    List<TimeEntry> findByEntryDate(LocalDate entryDate);
    List<TimeEntry> findByEntryDateBetween(LocalDate startDate, LocalDate endDate);
    List<TimeEntry> findByUserAndEntryDate(User user, LocalDate entryDate);
    List<TimeEntry> findByUserAndEntryDateBetween(User user, LocalDate startDate, LocalDate endDate);
    List<TimeEntry> findByProjectAndEntryDateBetween(Project project, LocalDate startDate, LocalDate endDate);
    List<TimeEntry> findByUserAndProjectAndEntryDateBetween(User user, Project project, LocalDate startDate, LocalDate endDate);
    
    @Query("SELECT t FROM TimeEntry t WHERE t.user IN ?1 AND t.entryDate BETWEEN ?2 AND ?3 AND (?4 IS NULL OR t.status = ?4)")
    List<TimeEntry> findByUsersAndEntryDateBetweenAndStatus(List<User> users, LocalDate startDate, LocalDate endDate, TimeEntryStatus status);
    
    @Query("SELECT t FROM TimeEntry t WHERE t.user IN ?1 AND t.entryDate BETWEEN ?2 AND ?3 AND t.status = ?4")
    List<TimeEntry> findByUsersAndEntryDateBetweenAndStatusStrict(List<User> users, LocalDate startDate, LocalDate endDate, TimeEntryStatus status);
    
    @Query("SELECT SUM(t.timeInMins) FROM TimeEntry t WHERE t.user = ?1 AND t.entryDate = ?2")
    Integer getTotalTimeByUserAndDate(User user, LocalDate date);

    @Query("SELECT SUM(t.timeInMins) FROM TimeEntry t WHERE t.user = ?1 AND t.entryDate = ?2 AND (t.isOvertime = false OR t.isOvertime IS NULL)")
    Integer getTotalNormalTimeByUserAndDate(User user, LocalDate date);
    
    @Query("SELECT SUM(t.timeInMins) FROM TimeEntry t WHERE t.user = ?1 AND t.project = ?2 AND t.entryDate BETWEEN ?3 AND ?4")
    Integer getTotalTimeByUserAndProjectAndDateRange(User user, Project project, LocalDate startDate, LocalDate endDate);

    List<TimeEntry> findByUser_IdAndProject_IdAndEntryDateBetween(Long userId, Long projectId, LocalDate startDate, LocalDate endDate);
    List<TimeEntry> findByUser_IdAndEntryDateBetween(Long userId, LocalDate startDate, LocalDate endDate);
    List<TimeEntry> findByProject_IdAndEntryDateBetween(Long projectId, LocalDate startDate, LocalDate endDate);
    @Query("SELECT t FROM TimeEntry t WHERE t.entryDate BETWEEN :startDate AND :endDate AND t.status = :status")
    List<TimeEntry> findAllByStatusStrictAndEntryDateBetween(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("status") TimeEntryStatus status
    );
    @Query("SELECT t FROM TimeEntry t WHERE t.entryDate BETWEEN :startDate AND :endDate AND (:status IS NULL OR t.status = :status)")
    List<TimeEntry> findAllByStatusOptionalAndEntryDateBetween(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("status") TimeEntryStatus status
    );


}
