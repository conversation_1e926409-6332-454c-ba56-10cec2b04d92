package com.vbs.capsAllocation.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class VunnoRequestDto {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("ldap")
    private String ldap;

    @JsonProperty("approvingLead")
    private String approvingLead;

    @JsonProperty("applicationType")
    private String applicationType;

    @JsonProperty("leaveType")
    private String leaveType;

    @JsonProperty("lvWfhDuration")
    private String lvWfhDuration;

    @JsonProperty("startDate")
    private String startDate;

    @JsonProperty("endDate")
    private String endDate;

    private String startDateTime;
    private String endDateTime;

    @JsonProperty("backupInfo")
    private String backupInfo;

    @JsonProperty("oooProof")
    private String oooProof;

    @JsonProperty("timesheetProof")
    private String timesheetProof;

    @JsonProperty("status")
    private String status;

    @JsonProperty("requestorName")
    private String requestorName;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("document")
    private String documentPath;

    @JsonProperty("role")
    private String role;

    @JsonProperty("timestamp")
    private LocalDateTime timestamp;

    public VunnoRequestDto(
            Long id,
            String ldap,
            String approvingLead,
            String applicationType,
            String leaveType,
            String lvWfhDuration,
            LocalDate startDate,
            LocalDate endDate,
            String backupInfo,
            String oooProof,
            String timesheetProof,
            String status,
            String requestorName,
            String reason,
            String documentPath,
            LocalDateTime timestamp
    ) {
        this.id = id;
        this.ldap = ldap;
        this.approvingLead = approvingLead;
        this.applicationType = applicationType;
        this.leaveType = leaveType;
        this.lvWfhDuration = lvWfhDuration;
        // Convert LocalDate → String
        this.startDate = startDate != null ? startDate.toString() : null;
        this.endDate = endDate != null ? endDate.toString() : null;
        this.backupInfo = backupInfo;
        this.oooProof = oooProof;
        this.timesheetProof = timesheetProof;
        this.status = status;
        this.requestorName = requestorName;
        this.reason = reason;
        this.documentPath = documentPath;
        this.timestamp = timestamp;
    }
}
