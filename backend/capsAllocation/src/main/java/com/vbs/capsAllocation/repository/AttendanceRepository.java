package com.vbs.capsAllocation.repository;

import com.vbs.capsAllocation.dto.AttendanceResponseDto;
import com.vbs.capsAllocation.model.Attendance;
import com.vbs.capsAllocation.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface AttendanceRepository extends JpaRepository<Attendance,Long> {

    @Query("SELECT FUNCTION('TO_CHAR', a.entryTimestamp, 'HH24:MI') FROM Attendance a WHERE a.id = :attendanceId")
    String findCheckInTimeById(@Param("attendanceId") String attendanceId);

    Optional<Attendance> findTopByEmployeeLdapAndEntryDateOrderByEntryTimestampDesc(
            String ldap,
            LocalDate entryDate
    );

    @Query("SELECT a FROM Attendance a JOIN a.employee e WHERE e.ldap = :ldap AND a.entryDate = :entryDate")
    List<Attendance> findByEmployeeLdapAndEntryDate(
            @Param("ldap") String ldap,
            @Param("entryDate") LocalDate entryDate);

    @Query("""
    SELECT new com.vbs.capsAllocation.dto.AttendanceResponseDto(
        a.id,
        e.ldap,
        e.team,
        CONCAT(e.firstName, ' ', e.lastName),
        a.entryDate,
        a.entryTimestamp,
        a.lateLoginReason,
        a.isOutsideOffice,
        a.isDefaulter,
        a.comment
    )
    FROM Attendance a
    JOIN a.employee e
    WHERE e.ldap = :ldap
      AND a.entryDate BETWEEN :startDate AND :endDate
""")
    List<AttendanceResponseDto> findByEmployeeLdapAndEntryDateBetween(
            @Param("ldap") String ldap,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    @Query("""
    SELECT new com.vbs.capsAllocation.dto.AttendanceResponseDto(
        a.id,
        e.ldap,
        e.team,
        CONCAT(e.firstName, ' ', e.lastName),
        a.entryDate,
        a.entryTimestamp,
        a.lateLoginReason,
        a.isOutsideOffice,
        a.isDefaulter,
        a.comment
    )
    FROM Attendance a
    JOIN a.employee e
    WHERE e.ldap IN :ldaps
      AND a.entryDate BETWEEN :startDate AND :endDate
""")
    List<AttendanceResponseDto> findByLdapsAndDateRange(
            @Param("ldaps") List<String> ldaps,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    List<Attendance> findByEmployeeLdapOrderByEntryDateDesc(String ldap);

    Optional<Attendance> findByEntryDate(LocalDate today);
}

