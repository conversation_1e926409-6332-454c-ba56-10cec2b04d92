package com.vbs.capsAllocation.controller;

import com.vbs.capsAllocation.dto.BaseResponse;
import com.vbs.capsAllocation.dto.VunnoRequestDto;
import com.vbs.capsAllocation.service.VunnoMgmtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.vbs.capsAllocation.util.LoggerUtil;


@RestController
@RequestMapping("/api/vunno")
public class VunnoApprovalController {

    @Autowired
    private VunnoMgmtService vunnoMgmtService;

    @PostMapping("/approve")
    public ResponseEntity<BaseResponse<String>> approveRequest(@RequestBody VunnoRequestDto requestDto,
                                                       @AuthenticationPrincipal UserDetails userDetails) {
        LoggerUtil.logDebug("Approving leave request for ID: {}", requestDto.getId());
        String resultMessage = vunnoMgmtService.approveRequest(requestDto, userDetails);
        return ResponseEntity.ok(BaseResponse.success(resultMessage));
    }

    @PostMapping("/reject")
    public ResponseEntity<BaseResponse<String>> rejectRequest(@RequestBody VunnoRequestDto requestDto,
                                                      @AuthenticationPrincipal UserDetails userDetails) {
        LoggerUtil.logDebug("Rejecting leave request for ID: {}", requestDto.getId());
        String resultMessage = vunnoMgmtService.rejectRequest(requestDto, userDetails);
        return ResponseEntity.ok(BaseResponse.success(resultMessage));
    }

    @PostMapping("/revoke")
    public ResponseEntity<BaseResponse<String>> revokeRequest(@RequestBody VunnoRequestDto requestDto,
                                                      @AuthenticationPrincipal UserDetails userDetails) {
        LoggerUtil.logDebug("Revoking leave request for ID: {}", requestDto.getId());
        String resultMessage = vunnoMgmtService.revokeRequest(requestDto, userDetails);
        return ResponseEntity.ok(BaseResponse.success(resultMessage));
    }
}
