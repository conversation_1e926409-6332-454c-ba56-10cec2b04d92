package com.vbs.capsAllocation.service;

import com.vbs.capsAllocation.dto.VunnoMgmtDto;
import com.vbs.capsAllocation.dto.VunnoRequestDto;
import com.vbs.capsAllocation.model.Employee;
import com.vbs.capsAllocation.model.EmployeeRelation;
import com.vbs.capsAllocation.model.User;
import com.vbs.capsAllocation.model.VunnoResponse;
import com.vbs.capsAllocation.repository.EmployeeRelationRepository;
import com.vbs.capsAllocation.repository.EmployeeRepository;
import com.vbs.capsAllocation.repository.LeaveUsageLogRepository;
import com.vbs.capsAllocation.repository.UserRepository;
import com.vbs.capsAllocation.util.EmailTemplateUtil;
import jakarta.mail.MessagingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class NotificationService {

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailTemplateUtil emailTemplateUtil;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private LeaveUsageLogRepository leaveUsageLogRepository;


    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeRelationRepository employeeRelationRepository;

    private static final String ACCOUNT_MANAGER_EMAIL = "<EMAIL>";

//    public void triggerRequestNotification(VunnoRequestDto requestDto, VunnoMgmtDto dto, List<Double> counts) {
//        try {
//            String ldap = requestDto.getLdap();
//            String role = requestDto.getRole(); // USER, LEAD, MANAGER from frontend
//            String managerLdap = dto.getManager(); // Program manager or reporting manager
//            String DOMAIN = "@google.com";
//
//            System.out.println("ROLE AND MANAGER: " + role + "  " + managerLdap);
//
//            // Check if WFH request is more than 3 days
//            boolean isWFHMoreThan3Days = isWFHMoreThan3Days(requestDto);
//
//            // Generate email body with disclaimer if needed
//            String emailBody = emailTemplateUtil.getVunnoNotificationEmail(requestDto, dto, counts, isWFHMoreThan3Days);
//
//            // Build recipient lists
//            Set<String> ccList = new HashSet<>();
//            Set<String> toList = new HashSet<>();
//            System.out.println("Program Alignment " + dto.getProgramAlignment());
//
//            List<Employee> programManagers = employeeRepository.findByPnseProgramAndLevelIgnoreCase(dto.getProgramAlignment(), "Program Manager");
//
//            // Route to account manager if WFH > 3 days, otherwise to regular manager
//            if (isWFHMoreThan3Days) {
//                toList.add(ACCOUNT_MANAGER_EMAIL);
//                SysttriggerRequestNotificationem.out.println("WFH request > 3 days, routing to account manager: " + ACCOUNT_MANAGER_EMAIL);
//                if (programManagers.isEmpty()) {
//                    ccList.add(dto.getManager() + DOMAIN); // fallback
//                }
//                else {
//                    ccList.addAll(
//                            programManagers.stream()
//                                    .map(pm -> pm.getLdap() + DOMAIN)
//                                    .collect(Collectors.toSet())
//                    );
//                }
//            }
//            else {
//                if (programManagers.isEmpty()) {
//                    toList.add(dto.getManager() + DOMAIN); // fallback
//                }
//                else {
//                    toList.addAll(
//                            programManagers.stream()
//                                    .map(pm -> pm.getLdap() + DOMAIN)
//                                    .collect(Collectors.toSet())
//                    );
//                }
//                System.out.println("ToList " + toList);
//            }
//            if ("USER".equalsIgnoreCase(role)) {
//                // TO: manager
//                // CC: requestor + all leads under the same manager
//                ccList.add(ldap + DOMAIN); // add requestor
//                List<Employee> underManager = employeeRepository.findByProgramManager(managerLdap);
//                for (Employee emp : underManager) {
//                    Optional<User> user = userRepository.findByUsername(emp.getLdap());
//                    if (user.isPresent() && user.get().getRole() != null &&
//                        user.get().getRole().toString().equals("LEAD")) {
//                        ccList.add(emp.getLdap() + DOMAIN);
//                    }
//                }
//            } else if ("LEAD".equalsIgnoreCase(role) || "MANAGER".equalsIgnoreCase(role)) {
//                // TO: manager
//                // CC: only the requestor
//                ccList.add(ldap + DOMAIN); // add requestor only
//            }
//
//            // Debug CC list
//            for (String cc : ccList) {
//                System.out.println("List of People in CC: " + cc);
//            }
//
//            // Email subject - include leave type for leave requests
//            String subjectPrefix = isWFHMoreThan3Days ? "Teamsphere [ACCOUNT MANAGER] " : "Teamsphere ";
//            String leaveTypeInfo = "";
//            if ("Leave".equalsIgnoreCase(requestDto.getApplicationType()) && requestDto.getLeaveType() != null) {
//                leaveTypeInfo = " (" + requestDto.getLeaveType() + ")";
//            }
//            String subject = String.format("%s%s Request%s | %s | %s - %s | %s",
//                    subjectPrefix,
//                    requestDto.getApplicationType(),
//                    leaveTypeInfo,
//                    ldap,
//                    requestDto.getStartDate(),
//                    requestDto.getEndDate(),
//                    requestDto.getLvWfhDuration()
//            );
//
//            // Send email ccList
//            emailService.sendEmail(
//                    new ArrayList<>(toList),
//                    new ArrayList<>(ccList),
//                    subject,
//                    emailBody
//            );
//
//        } catch (Exception e) {
//            System.err.println("Error sending approval notification email: " + e.getMessage());
//            e.printStackTrace();
//            throw new RuntimeException("Failed to send approval notification email", e);
//        }
//    }

    public void triggerRequestNotification(VunnoRequestDto requestDto, VunnoMgmtDto dto, List<Double> counts) {
        try {
            final String DOMAIN = "@google.com";
            String ldap = requestDto.getLdap();

            boolean isWFHMoreThan3Days = isWFHMoreThan3Days(requestDto);
            String emailBody = emailTemplateUtil.getVunnoNotificationEmail(requestDto, dto, counts, isWFHMoreThan3Days);

            // Use Sets to avoid duplicates
            Set<String> toSet = new HashSet<>();
            Set<String> ccSet = new HashSet<>();

            // Always CC requestor
            ccSet.add(ldap + DOMAIN);

            // Employee (primary manager / primary lead)
            Employee emp = employeeRepository.findByLdap(ldap)
                    .orElseThrow(() -> new RuntimeException("Employee not found: " + ldap));

            // Primary manager -> TO (if present)
            boolean managerAdded = false;
            if (emp.getProgramManager() != null && !emp.getProgramManager().isBlank()) {
                toSet.add(emp.getProgramManager() + DOMAIN);
                managerAdded = true;
            }

            // Primary lead -> CC
            if (emp.getLead() != null && !emp.getLead().isBlank()) {
                ccSet.add(emp.getLead() + DOMAIN);
            }

            // Secondary manager / lead from employee_relation
            List<EmployeeRelation> relations = employeeRelationRepository.findByEmployeeId(emp.getId());
            for (EmployeeRelation rel : relations) {
                if (!Boolean.TRUE.equals(rel.getIsActive())) continue;

                String relationTypeName = null;
                if (rel.getRelationType() != null && rel.getRelationType().getName() != null) {
                    relationTypeName = rel.getRelationType().getName();
                } else {
                    relationTypeName = rel.getRelationValue();
                }

                Employee related = rel.getRelatedEmployee();
                if (related == null) continue;

                if ("MANAGER".equalsIgnoreCase(relationTypeName)) {
                    toSet.add(related.getLdap() + DOMAIN);
                    managerAdded = true;
                } else if ("LEAD".equalsIgnoreCase(relationTypeName)) {
                    ccSet.add(related.getLdap() + DOMAIN);
                }
            }

            // If requestor is a LEAD, ensure their manager/secondary manager was included
            Optional<User> userOpt = userRepository.findByUsername(ldap);
            if (userOpt.isPresent() && userOpt.get().getRole() != null &&
                    "LEAD".equalsIgnoreCase(userOpt.get().getRole().toString())) {
                if (!managerAdded) {
                    // Try again to find any manager relations (defensive: same check as above)
                    for (EmployeeRelation rel : relations) {
                        if (!Boolean.TRUE.equals(rel.getIsActive())) continue;
                        String relationTypeName = rel.getRelationType() != null ? rel.getRelationType().getName() : rel.getRelationValue();
                        if ("MANAGER".equalsIgnoreCase(relationTypeName) && rel.getRelatedEmployee() != null) {
                            toSet.add(rel.getRelatedEmployee().getLdap() + DOMAIN);
                            managerAdded = true;
                            break;
                        }
                    }
                }
                if (!managerAdded) {
                    // Nothing to add — log so admins/developers can check data completeness
                    System.out.println("Warning: Requestor is LEAD but no primary or secondary manager found for " + ldap);
                }
            }

            // Special case: WFH > 3 days = notify account manager in CC
            if (isWFHMoreThan3Days) {
                toSet.add(ACCOUNT_MANAGER_EMAIL);
            }

            // Build subject
            String leaveTypeInfo = ("Leave".equalsIgnoreCase(requestDto.getApplicationType()) &&
                    requestDto.getLeaveType() != null)
                    ? " (" + requestDto.getLeaveType() + ")" : "";

            String subject = String.format("Teamsphere Request%s | %s | %s - %s | %s",
                    leaveTypeInfo,
                    ldap,
                    requestDto.getStartDate(),
                    requestDto.getEndDate(),
                    requestDto.getLvWfhDuration()
            );

            // Convert sets to lists and send
            List<String> toList = new ArrayList<>(toSet); // may be empty if no manager configured
            List<String> ccList = new ArrayList<>(ccSet);

            // Debug
            System.out.println("Sending request mail TO: " + toList);
            System.out.println("CC: " + ccList);

            emailService.sendEmail(toList, ccList, subject, emailBody);

        } catch (Exception e) {
            System.err.println("Error sending request notification email: " + e.getMessage());
            throw new RuntimeException("Failed to send request notification email", e);
        }
    }

    public void triggerApprovalNotification(VunnoRequestDto requestDto, VunnoResponse response, String role, VunnoMgmtDto dto) {
        try {
            final String DOMAIN = "@google.com";
            String ldap = requestDto.getLdap();           // Requestor
            String managerLdap = dto.getManager();       // Primary manager from DTO (if available)

            Set<String> ccSet = new HashSet<>();
            List<String> toList = new ArrayList<>();

            // TO: vbs group (approval mails)
            toList.add("<EMAIL>");

            // CC everyone else (requestor, primary manager, primary lead, secondary manager/lead)
            ccSet.add(ldap + DOMAIN);
            if (managerLdap != null && !managerLdap.isBlank()) {
                ccSet.add(managerLdap + DOMAIN);
            }

            // Primary lead from employee table
            Employee emp = employeeRepository.findByLdap(ldap)
                    .orElse(null);
            if (emp != null) {
                if (emp.getLead() != null && !emp.getLead().isBlank()) {
                    ccSet.add(emp.getLead() + DOMAIN);
                }

                // secondary relations
                List<EmployeeRelation> relations = employeeRelationRepository.findByEmployeeId(emp.getId());
                for (EmployeeRelation rel : relations) {
                    if (!Boolean.TRUE.equals(rel.getIsActive())) continue;

                    String relationTypeName = null;
                    if (rel.getRelationType() != null && rel.getRelationType().getName() != null) {
                        relationTypeName = rel.getRelationType().getName();
                    } else {
                        relationTypeName = rel.getRelationValue();
                    }

                    Employee related = rel.getRelatedEmployee();
                    if (related == null) continue;

                    if ("MANAGER".equalsIgnoreCase(relationTypeName)) {
                        ccSet.add(related.getLdap() + DOMAIN);
                    } else if ("LEAD".equalsIgnoreCase(relationTypeName)) {
                        ccSet.add(related.getLdap() + DOMAIN);
                    }
                }
            }

            // Subject and body
            String subject = String.format("Teamsphere [REQUEST APPROVED] | %s | %s | %s - %s",
                    ldap,
                    response.getApplicationType(),
                    response.getFromDate(),
                    response.getToDate()
            );

            String emailBody = emailTemplateUtil.getVunnoApprovalEmail(
                    response.getBackup(),
                    response.getApplicationType(),
                    response.getFromDate().toString(),
                    response.getToDate().toString(),
                    response.getDuration()
            );

            // Debug
            System.out.println("Sending approval mail TO: " + toList);
            System.out.println("CC: " + new ArrayList<>(ccSet));

            emailService.sendEmail(toList, new ArrayList<>(ccSet), subject, emailBody);

        } catch (Exception e) {
            System.err.println("Error sending approval notification email: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send approval notification email", e);
        }
    }


    /**
     * Calculate the number of days for a WFH request based on duration and dates
     * @param requestDto The WFH request DTO
     * @return The number of days for the WFH request
     */
    public double calculateWFHDays(VunnoRequestDto requestDto) {
        String duration = requestDto.getLvWfhDuration();
        if (duration == null) {
            throw new RuntimeException("Duration is required for WFH calculation.");
        }

        duration = duration.trim();

        switch (duration) {
            case "Full Day":
                return 1.0;
            case "Half Day AM":
            case "Half Day PM":
                return 0.5;
            case "Multiple Days":
                if (requestDto.getStartDate() == null || requestDto.getEndDate() == null) {
                    throw new RuntimeException("Start date and end date are required for Multiple Days WFH.");
                }
                try {
                    LocalDate startDate = LocalDate.parse(requestDto.getStartDate());
                    LocalDate endDate = LocalDate.parse(requestDto.getEndDate());
                    long days = ChronoUnit.DAYS.between(startDate, endDate) + 1; // Inclusive
                    return (double) days;
                } catch (Exception e) {
                    throw new RuntimeException("Invalid date format for WFH calculation: " + e.getMessage());
                }
            default:
                throw new RuntimeException("Invalid WFH duration: " + duration);
        }
    }

    /**
     * Check if a WFH request exceeds 3 days (strictly greater than 3)
     * @param requestDto The WFH request DTO
     * @return true if the request is for more than 3 days, false otherwise
     */
    public boolean isWFHMoreThan3Days(VunnoRequestDto requestDto) {
        if (!"Work From Home".equalsIgnoreCase(requestDto.getApplicationType())) {
            return false;
        }

        try {
            double days = calculateWFHDays(requestDto);
            // Changed to strictly greater than 3 (so 3 days exactly will NOT trigger account manager email)
            return days > 3.0;
        } catch (Exception e) {
            System.err.println("Error calculating WFH days for validation: " + e.getMessage());
            return false;
        }
    }

}
