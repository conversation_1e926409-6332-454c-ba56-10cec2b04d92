spring.datasource.url=****************************************************
spring.datasource.username=postgres
spring.datasource.password=voyage
#voyage
spring.jpa.hibernate.ddl-auto=update
# Disable schema validation during startup to allow our custom migration to run first
spring.jpa.properties.hibernate.schema_validation.enabled=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.web.resources.static-locations=file:uploads/profile-pics
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=1MB
google.sheets.spreadsheetId=14USVFIoRJQzSgKA9_DI3_c_rOJy7G2W9txJRNRDCfAw
google.sheets.credentials=classpath:ops-excellence-a969197613f8.json
google.sheets.sheetName=Response
# Logging Configuration
logging.level.root=INFO
logging.level.org.springframework=INFO
logging.level.com.vbs.capsAllocation=DEBUG

# Custom log files
logging.file.name=logs/teamsphere.log
logging.file.max-size=10MB
logging.file.max-history=30
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Custom log files for error and debug
logging.level.com.vbs.capsAllocation.error=ERROR
logging.level.com.vbs.capsAllocation.debug=DEBUG
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=vief vqui luoy irzk
#caeanvyahiqpczyg
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.debug=true

# Database Backup Configuration
# Backup directory - defaults to system temp directory if not specified
# For production, use a writable directory like /tmp/teamsphere_backups
backup.directory=${BACKUP_DIRECTORY:#{systemProperties['java.io.tmpdir']}/teamsphere_backups}
backup.share.email=<EMAIL>

# Google Drive Integration Configuration
# Set to false to disable Google Drive upload (useful for environments with network restrictions)
backup.googledrive.enabled=${BACKUP_GOOGLEDRIVE_ENABLED:true}
# Timeout for Google Drive operations in seconds
backup.googledrive.timeout=${BACKUP_GOOGLEDRIVE_TIMEOUT:30}

# Server timeout configurations for long-running operations
server.tomcat.connection-timeout=300000
server.tomcat.keep-alive-timeout=300000
server.tomcat.max-keep-alive-requests=100

spring.jpa.properties.hibernate.default_schema=public

# LOB handling configuration for PostgreSQL
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.open-in-view=false
spring.jackson.time-zone=Asia/Kolkata
